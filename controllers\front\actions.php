<?php
/**
 * CompareX - AJAX Actions Controller
 * 
 * <AUTHOR>
 * @copyright Copyright (c) 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

class StProductCompareActionsModuleFrontController extends ModuleFrontController
{
    public $ssl = true;
    public $ajax = true;

    public function initContent()
    {
        parent::initContent();

        $action = Tools::getValue('action');
        
        switch ($action) {
            case 'add':
                $this->addProduct();
                break;
            case 'remove':
                $this->removeProduct();
                break;
            case 'clear':
                $this->clearComparison();
                break;
            case 'get':
                $this->getComparisonData();
                break;
            case 'count':
                $this->getComparisonCount();
                break;
            default:
                $this->ajaxRender(json_encode([
                    'success' => false,
                    'message' => $this->module->l('Invalid action')
                ]));
        }
    }

    private function addProduct()
    {
        $id_product = (int)Tools::getValue('id_product');
        
        if (!$id_product || !Product::existsInDatabase($id_product, 'product')) {
            $this->ajaxRender(json_encode([
                'success' => false,
                'message' => $this->module->l('Invalid product')
            ]));
            return;
        }

        $comparison = $this->getOrCreateComparison();
        $products = json_decode($comparison['products'], true) ?: [];
        
        // Check if product is already in comparison
        if (in_array($id_product, $products)) {
            $this->ajaxRender(json_encode([
                'success' => false,
                'message' => $this->module->l('Product already in comparison'),
                'count' => count($products)
            ]));
            return;
        }

        // Check maximum products limit
        $max_products = (int)Configuration::get('ST_COMPARE_MAX_PRODUCTS');
        if (count($products) >= $max_products) {
            $this->ajaxRender(json_encode([
                'success' => false,
                'message' => sprintf($this->module->l('Maximum %d products allowed in comparison'), $max_products),
                'count' => count($products)
            ]));
            return;
        }

        // Add product to comparison
        $products[] = $id_product;
        $this->updateComparison($comparison['id_session'], $products);

        // Get product data for response
        $product = new Product($id_product, false, $this->context->language->id);
        $product_data = [
            'id_product' => $product->id,
            'name' => $product->name,
            'link' => $this->context->link->getProductLink($product),
            'image' => $this->context->link->getImageLink($product->link_rewrite, $product->getCoverWs(), ImageType::getFormattedName('home'))
        ];

        $this->ajaxRender(json_encode([
            'success' => true,
            'message' => $this->module->l('Product added to comparison'),
            'count' => count($products),
            'product' => $product_data,
            'products' => $this->getProductsData($products)
        ]));
    }

    private function removeProduct()
    {
        $id_product = (int)Tools::getValue('id_product');
        
        if (!$id_product) {
            $this->ajaxRender(json_encode([
                'success' => false,
                'message' => $this->module->l('Invalid product')
            ]));
            return;
        }

        $comparison = $this->getOrCreateComparison();
        $products = json_decode($comparison['products'], true) ?: [];
        
        // Remove product from comparison
        $products = array_values(array_filter($products, function($pid) use ($id_product) {
            return $pid != $id_product;
        }));

        $this->updateComparison($comparison['id_session'], $products);

        $this->ajaxRender(json_encode([
            'success' => true,
            'message' => $this->module->l('Product removed from comparison'),
            'count' => count($products),
            'products' => $this->getProductsData($products)
        ]));
    }

    private function clearComparison()
    {
        $comparison = $this->getOrCreateComparison();
        $this->updateComparison($comparison['id_session'], []);

        $this->ajaxRender(json_encode([
            'success' => true,
            'message' => $this->module->l('Comparison cleared'),
            'count' => 0,
            'products' => []
        ]));
    }

    private function getComparisonData()
    {
        $comparison = $this->getOrCreateComparison();
        $products = json_decode($comparison['products'], true) ?: [];

        $this->ajaxRender(json_encode([
            'success' => true,
            'count' => count($products),
            'products' => $this->getProductsData($products)
        ]));
    }

    private function getComparisonCount()
    {
        $comparison = $this->getOrCreateComparison();
        $products = json_decode($comparison['products'], true) ?: [];

        $this->ajaxRender(json_encode([
            'success' => true,
            'count' => count($products)
        ]));
    }

    private function getOrCreateComparison()
    {
        $session_token = $this->getSessionToken();
        
        $sql = 'SELECT * FROM `' . _DB_PREFIX_ . 'st_compare_session` 
                WHERE `session_token` = "' . pSQL($session_token) . '"';
        
        $comparison = Db::getInstance()->getRow($sql);
        
        if (!$comparison) {
            // Create new comparison session
            $data = [
                'session_token' => $session_token,
                'id_customer' => $this->context->customer->isLogged() ? $this->context->customer->id : null,
                'id_guest' => $this->context->customer->isLogged() ? null : $this->context->cookie->id_guest,
                'products' => json_encode([]),
                'date_add' => date('Y-m-d H:i:s'),
                'date_upd' => date('Y-m-d H:i:s')
            ];
            
            if (Db::getInstance()->insert('st_compare_session', $data)) {
                $comparison = [
                    'id_session' => Db::getInstance()->Insert_ID(),
                    'session_token' => $session_token,
                    'products' => json_encode([])
                ];
            }
        }
        
        return $comparison;
    }

    private function updateComparison($id_session, $products)
    {
        $data = [
            'products' => json_encode($products),
            'date_upd' => date('Y-m-d H:i:s')
        ];
        
        return Db::getInstance()->update('st_compare_session', $data, 'id_session = ' . (int)$id_session);
    }

    private function getSessionToken()
    {
        if (!isset($this->context->cookie->st_compare_token)) {
            $this->context->cookie->st_compare_token = md5(uniqid(rand(), true));
        }
        
        return $this->context->cookie->st_compare_token;
    }

    private function getProductsData($product_ids)
    {
        if (empty($product_ids)) {
            return [];
        }

        $products = [];
        foreach ($product_ids as $id_product) {
            $product = new Product($id_product, false, $this->context->language->id);
            if (Validate::isLoadedObject($product)) {
                $products[] = [
                    'id_product' => $product->id,
                    'name' => $product->name,
                    'link' => $this->context->link->getProductLink($product),
                    'image' => $this->context->link->getImageLink($product->link_rewrite, $product->getCoverWs(), ImageType::getFormattedName('home')),
                    'price' => Product::getPriceStatic($product->id, true, null, 2),
                    'price_formatted' => Tools::displayPrice(Product::getPriceStatic($product->id, true)),
                    'reference' => $product->reference,
                    'description_short' => $product->description_short
                ];
            }
        }
        
        return $products;
    }
}
