{*
* CompareX - Debug Buttons Template
* Add this to any page to test CompareX functionality
*}

{if isset($smarty.get.debug) || isset($smarty.get.compare_test)}
<div id="st-compare-debug" style="position: fixed; top: 10px; right: 10px; z-index: 10000; background: white; padding: 15px; border: 2px solid #007cba; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.2); font-family: Arial, sans-serif;">
    <h6 style="margin: 0 0 10px 0; color: #007cba; font-size: 14px; font-weight: bold;">🚀 CompareX Debug</h6>

    <div style="margin-bottom: 10px; font-size: 12px;">
        <div>Status: <span id="debug-status" style="color: #666;">Loading...</span></div>
        <div>Count: <span id="debug-count" style="font-weight: bold; color: #007cba;">0</span></div>
        <div>Footer: <span id="debug-footer" style="color: #666;">Checking...</span></div>
    </div>

    <button onclick="testAddToCompare(1)" style="display: block; width: 100%; margin: 3px 0; padding: 6px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
        ➕ Add Product 1
    </button>
    <button onclick="testAddToCompare(2)" style="display: block; width: 100%; margin: 3px 0; padding: 6px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
        ➕ Add Product 2
    </button>
    <button onclick="showCompareFooter()" style="display: block; width: 100%; margin: 3px 0; padding: 6px; background: #ffc107; color: black; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
        👁️ Show Footer
    </button>
    <button onclick="clearComparison()" style="display: block; width: 100%; margin: 3px 0; padding: 6px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
        🗑️ Clear All
    </button>

    <button onclick="document.getElementById('st-compare-debug').style.display='none'" style="display: block; width: 100%; margin: 8px 0 0 0; padding: 4px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 11px;">
        ❌ Hide Debug
    </button>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update debug info
    function updateDebugInfo() {
        const statusEl = document.getElementById('debug-status');
        const countEl = document.getElementById('debug-count');
        const footerEl = document.getElementById('debug-footer');

        if (statusEl) {
            if (typeof window.compareX !== 'undefined') {
                statusEl.textContent = 'Ready ✅';
                statusEl.style.color = '#28a745';
            } else {
                statusEl.textContent = 'Not Loaded ❌';
                statusEl.style.color = '#dc3545';
            }
        }

        if (countEl) {
            const compareCount = document.getElementById('st-compare-count');
            if (compareCount) {
                countEl.textContent = compareCount.textContent;
            }
        }

        if (footerEl) {
            const footer = document.getElementById('st-compare-footer');
            if (footer) {
                footerEl.textContent = 'Found ✅';
                footerEl.style.color = '#28a745';
            } else {
                footerEl.textContent = 'Missing ❌';
                footerEl.style.color = '#dc3545';
            }
        }
    }

    // Update every second
    setInterval(updateDebugInfo, 1000);

    // Initial update
    setTimeout(updateDebugInfo, 500);
});
</script>
{/if}
