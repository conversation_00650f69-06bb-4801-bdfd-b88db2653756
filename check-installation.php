<?php
/**
 * CompareX - Installation Check
 * 
 * Access this file directly: yoursite.com/modules/stproductcompare/check-installation.php
 * This will verify if the module is properly installed and configured
 */

// Include PrestaShop configuration
$prestashop_path = dirname(dirname(dirname(__FILE__)));
require_once($prestashop_path . '/config/config.inc.php');

?>
<!DOCTYPE html>
<html>
<head>
    <title>CompareX Installation Check</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f8f9fa; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        h1 { color: #007cba; }
        h2 { color: #333; border-bottom: 2px solid #007cba; padding-bottom: 10px; }
        .test-button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .test-button:hover { background: #005a8b; }
        #test-results { margin-top: 20px; padding: 20px; background: #f8f9fa; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 CompareX Installation Check</h1>
        
        <h2>Module Status</h2>
        
        <?php
        // Check if module is installed
        $module_installed = Module::isInstalled('stproductcompare');
        $module_enabled = Module::isEnabled('stproductcompare');
        
        if ($module_installed) {
            echo '<div class="status success">✅ Module is installed</div>';
        } else {
            echo '<div class="status error">❌ Module is NOT installed</div>';
        }
        
        if ($module_enabled) {
            echo '<div class="status success">✅ Module is enabled</div>';
        } else {
            echo '<div class="status error">❌ Module is NOT enabled</div>';
        }
        
        // Check configuration
        if ($module_installed) {
            $compare_enabled = Configuration::get('ST_COMPARE_ENABLE');
            $sticky_footer = Configuration::get('ST_COMPARE_STICKY_FOOTER');
            $theme = Configuration::get('ST_COMPARE_THEME');
            $max_products = Configuration::get('ST_COMPARE_MAX_PRODUCTS');
            
            echo '<h2>Configuration</h2>';
            
            if ($compare_enabled) {
                echo '<div class="status success">✅ CompareX is enabled</div>';
            } else {
                echo '<div class="status error">❌ CompareX is disabled - Go to module configuration and enable it</div>';
            }
            
            if ($sticky_footer) {
                echo '<div class="status success">✅ Sticky footer is enabled</div>';
            } else {
                echo '<div class="status error">❌ Sticky footer is disabled - Go to module configuration and enable it</div>';
            }
            
            echo '<div class="status info">ℹ️ Theme: ' . ($theme ? $theme : 'Not set') . '</div>';
            echo '<div class="status info">ℹ️ Max products: ' . ($max_products ? $max_products : 'Not set') . '</div>';
            
            // Check database tables
            echo '<h2>Database</h2>';
            
            $tables_exist = true;
            $tables = ['st_compare_session', 'st_product_compare'];
            
            foreach ($tables as $table) {
                $table_exists = Db::getInstance()->executeS("SHOW TABLES LIKE '" . _DB_PREFIX_ . $table . "'");
                if ($table_exists) {
                    echo '<div class="status success">✅ Table ' . _DB_PREFIX_ . $table . ' exists</div>';
                } else {
                    echo '<div class="status error">❌ Table ' . _DB_PREFIX_ . $table . ' missing</div>';
                    $tables_exist = false;
                }
            }
            
            // Check files
            echo '<h2>Files</h2>';
            
            $files_to_check = [
                'stproductcompare.php' => 'Main module file',
                'views/css/front.css' => 'Frontend CSS',
                'views/js/front.js' => 'Frontend JavaScript',
                'controllers/front/actions.php' => 'AJAX controller',
                'controllers/front/compare.php' => 'Comparison page controller'
            ];
            
            foreach ($files_to_check as $file => $description) {
                if (file_exists(__DIR__ . '/' . $file)) {
                    echo '<div class="status success">✅ ' . $description . ' (' . $file . ')</div>';
                } else {
                    echo '<div class="status error">❌ Missing: ' . $description . ' (' . $file . ')</div>';
                }
            }
            
            // URLs
            echo '<h2>URLs</h2>';
            
            $context = Context::getContext();
            $ajax_url = $context->link->getModuleLink('stproductcompare', 'actions');
            $compare_url = $context->link->getModuleLink('stproductcompare', 'compare');
            $test_url = $context->link->getModuleLink('stproductcompare', 'test');
            
            echo '<div class="status info">ℹ️ AJAX URL: <a href="' . $ajax_url . '" target="_blank">' . $ajax_url . '</a></div>';
            echo '<div class="status info">ℹ️ Compare URL: <a href="' . $compare_url . '" target="_blank">' . $compare_url . '</a></div>';
            echo '<div class="status info">ℹ️ Test URL: <a href="' . $test_url . '" target="_blank">' . $test_url . '</a></div>';
        }
        ?>
        
        <h2>Live Test</h2>
        <p>Click these buttons to test the sticky footer functionality:</p>
        
        <button class="test-button" onclick="testAddProduct()">Add Test Product</button>
        <button class="test-button" onclick="showFooter()">Show Footer</button>
        <button class="test-button" onclick="clearTest()">Clear Test</button>
        
        <div id="test-results">
            <strong>Test Results:</strong><br>
            <div id="test-log">Click a test button to start...</div>
        </div>
        
        <h2>Quick Fixes</h2>
        <div class="status warning">
            <strong>If sticky footer is not showing:</strong><br>
            1. Make sure module is installed and enabled<br>
            2. Check that "Enable CompareX" and "Enable Sticky Footer" are both ON in module configuration<br>
            3. Clear PrestaShop cache (Advanced Parameters > Performance > Clear Cache)<br>
            4. Check browser console (F12) for JavaScript errors<br>
            5. Try the test buttons above
        </div>
        
        <div class="status info">
            <strong>Manual Test:</strong><br>
            1. Go to any product page<br>
            2. Look for "Add to Compare" button<br>
            3. Click it - sticky footer should appear at bottom<br>
            4. If not working, use the test buttons above
        </div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += '<br>[' + timestamp + '] ' + message;
        }
        
        function testAddProduct() {
            log('Testing add product...');
            
            // Check if test functions exist
            if (typeof window.testAddToCompare === 'function') {
                window.testAddToCompare(999);
                log('✅ Test product added successfully');
            } else {
                log('❌ Test function not found - module may not be loaded');
                
                // Try to manually show footer
                const footer = document.getElementById('st-compare-footer');
                if (footer) {
                    footer.style.transform = 'translateY(0)';
                    footer.style.display = 'block';
                    log('✅ Manually showed existing footer');
                } else {
                    log('❌ Sticky footer element not found');
                }
            }
        }
        
        function showFooter() {
            log('Testing show footer...');
            
            if (typeof window.showCompareFooter === 'function') {
                window.showCompareFooter();
                log('✅ Show footer function called');
            } else {
                log('❌ Show footer function not found');
            }
        }
        
        function clearTest() {
            log('Testing clear...');
            
            if (typeof window.clearComparison === 'function') {
                window.clearComparison();
                log('✅ Clear function called');
            } else {
                log('❌ Clear function not found');
            }
        }
        
        // Check if CompareX is loaded
        setTimeout(function() {
            if (typeof window.testAddToCompare === 'function') {
                log('✅ CompareX functions are available');
            } else {
                log('⚠️ CompareX functions not found - module may not be fully loaded');
            }
            
            // Check if sticky footer exists
            if (document.getElementById('st-compare-footer')) {
                log('✅ Sticky footer HTML element found');
            } else {
                log('⚠️ Sticky footer HTML element not found');
            }
        }, 2000);
    </script>
</body>
</html>
