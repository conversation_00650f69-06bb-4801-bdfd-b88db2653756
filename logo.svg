<?xml version="1.0" encoding="UTF-8"?>
<svg width="64" height="64" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#007cba;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ff6b6b;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="32" cy="32" r="30" fill="url(#grad1)" stroke="#ffffff" stroke-width="2"/>
  
  <!-- Balance scale icon -->
  <g fill="#ffffff" stroke="#ffffff" stroke-width="1">
    <!-- Scale base -->
    <rect x="30" y="45" width="4" height="8" rx="2"/>
    <rect x="24" y="51" width="16" height="3" rx="1.5"/>
    
    <!-- Scale arm -->
    <rect x="16" y="30" width="32" height="2" rx="1"/>
    
    <!-- Scale center -->
    <circle cx="32" cy="31" r="3" fill="#ffffff"/>
    
    <!-- Left scale pan -->
    <path d="M 20 32 L 16 38 L 24 38 Z" fill="#ffffff"/>
    <rect x="18" y="30" width="4" height="2" rx="1"/>
    
    <!-- Right scale pan -->
    <path d="M 44 32 L 40 38 L 48 38 Z" fill="#ffffff"/>
    <rect x="42" y="30" width="4" height="2" rx="1"/>
    
    <!-- Products on scales -->
    <circle cx="20" cy="26" r="3" fill="#ffffff" opacity="0.8"/>
    <circle cx="44" cy="26" r="3" fill="#ffffff" opacity="0.8"/>
  </g>
  
  <!-- CompareX text -->
  <text x="32" y="58" font-family="Arial, sans-serif" font-size="8" font-weight="bold" text-anchor="middle" fill="#007cba">CompareX</text>
</svg>
