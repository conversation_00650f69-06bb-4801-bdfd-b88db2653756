<?php
/**
 * CompareX - English Translations
 * 
 * <AUTHOR>
 * @copyright Copyright (c) 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

global $_MODULE;
$_MODULE = [];

// Module information
$_MODULE['<{stproductcompare}prestashop>stproductcompare_'] = 'CompareX - Advanced Product Comparison';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_description'] = 'Revolutionary product comparison module with sticky footer, quick actions, and enhanced user experience for PrestaShop 1.7 to 9.0';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_confirm_uninstall'] = 'Are you sure you want to uninstall CompareX? All comparison data will be lost.';

// Configuration
$_MODULE['<{stproductcompare}prestashop>stproductcompare_settings_updated'] = 'Settings updated successfully!';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_enable'] = 'Enable CompareX';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_enable_desc'] = 'Enable or disable the product comparison functionality';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_sticky_footer'] = 'Enable Sticky Footer';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_sticky_footer_desc'] = 'Show a sticky footer with comparison products';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_theme'] = 'Theme';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_theme_desc'] = 'Choose a pre-made theme for the comparison interface';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_max_products'] = 'Maximum Products';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_max_products_desc'] = 'Maximum number of products that can be compared at once';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_popup_mode'] = 'Popup Mode';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_popup_mode_desc'] = 'Open comparison page in a popup window';

// Buttons and actions
$_MODULE['<{stproductcompare}prestashop>stproductcompare_add_to_compare'] = 'Add to Compare';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_add_to_comparison'] = 'Add to comparison';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_compare'] = 'Compare';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_compare_now'] = 'Compare Now';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_in_comparison'] = 'In Comparison';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_remove'] = 'Remove';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_clear_all'] = 'Clear All';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_clear_comparison'] = 'Clear Comparison';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_print_comparison'] = 'Print Comparison';

// Messages
$_MODULE['<{stproductcompare}prestashop>stproductcompare_product_added'] = 'Product added to comparison';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_product_removed'] = 'Product removed from comparison';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_comparison_cleared'] = 'Comparison cleared';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_product_already_in'] = 'Product already in comparison';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_maximum_reached'] = 'Maximum %d products allowed in comparison';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_invalid_product'] = 'Invalid product';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_invalid_action'] = 'Invalid action';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_no_products'] = 'No products to compare';

// Comparison page
$_MODULE['<{stproductcompare}prestashop>stproductcompare_product_comparison'] = 'Product Comparison';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_comparing_products'] = 'Comparing %d Products';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_no_products_message'] = 'No products to compare. Start adding products to your comparison.';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_continue_shopping'] = 'Continue Shopping';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_view_product'] = 'View Product';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_feature'] = 'Feature';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_price'] = 'Price';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_reference'] = 'Reference';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_description'] = 'Description';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_reviews'] = 'Reviews';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_no_reviews'] = 'No reviews';

// Filter buttons
$_MODULE['<{stproductcompare}prestashop>stproductcompare_highlight_differences'] = 'Highlight Differences';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_highlight_similarities'] = 'Highlight Similarities';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_hide_differences'] = 'Hide Differences';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_hide_similarities'] = 'Hide Similarities';

// Theme names
$_MODULE['<{stproductcompare}prestashop>stproductcompare_theme_default'] = 'Default Blue';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_theme_dark'] = 'Dark Mode';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_theme_minimal'] = 'Minimal';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_theme_colorful'] = 'Colorful';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_theme_custom'] = 'Custom Colors';

// Configuration labels
$_MODULE['<{stproductcompare}prestashop>stproductcompare_enabled'] = 'Enabled';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_disabled'] = 'Disabled';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_products'] = 'products';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_save'] = 'Save';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_back_to_list'] = 'Back to list';
$_MODULE['<{stproductcompare}prestashop>stproductcompare_settings'] = 'CompareX Settings';
