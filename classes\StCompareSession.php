<?php
/**
 * CompareX - Comparison Session Class
 * 
 * <AUTHOR>
 * @copyright Copyright (c) 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

class StCompareSession extends ObjectModel
{
    public $id_session;
    public $session_token;
    public $id_customer;
    public $id_guest;
    public $products;
    public $date_add;
    public $date_upd;

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = [
        'table' => 'st_compare_session',
        'primary' => 'id_session',
        'fields' => [
            'session_token' => ['type' => self::TYPE_STRING, 'validate' => 'isGenericName', 'required' => true, 'size' => 64],
            'id_customer' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedId'],
            'id_guest' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedId'],
            'products' => ['type' => self::TYPE_STRING, 'validate' => 'isJson'],
            'date_add' => ['type' => self::TYPE_DATE, 'validate' => 'isDate'],
            'date_upd' => ['type' => self::TYPE_DATE, 'validate' => 'isDate'],
        ],
    ];

    /**
     * Get comparison session by token
     *
     * @param string $token
     * @return StCompareSession|false
     */
    public static function getByToken($token)
    {
        $sql = 'SELECT id_session FROM `' . _DB_PREFIX_ . 'st_compare_session` 
                WHERE `session_token` = "' . pSQL($token) . '"';
        
        $id_session = Db::getInstance()->getValue($sql);
        
        if ($id_session) {
            return new self($id_session);
        }
        
        return false;
    }

    /**
     * Get products in this comparison session
     *
     * @return array
     */
    public function getProducts()
    {
        $product_ids = json_decode($this->products, true);
        
        if (!is_array($product_ids) || empty($product_ids)) {
            return [];
        }

        $products = [];
        foreach ($product_ids as $id_product) {
            $product = new Product($id_product, false, Context::getContext()->language->id);
            if (Validate::isLoadedObject($product)) {
                $products[] = $product;
            }
        }

        return $products;
    }

    /**
     * Add product to comparison
     *
     * @param int $id_product
     * @return bool
     */
    public function addProduct($id_product)
    {
        $products = json_decode($this->products, true) ?: [];
        
        if (!in_array($id_product, $products)) {
            $products[] = (int)$id_product;
            $this->products = json_encode($products);
            $this->date_upd = date('Y-m-d H:i:s');
            
            return $this->update();
        }
        
        return true;
    }

    /**
     * Remove product from comparison
     *
     * @param int $id_product
     * @return bool
     */
    public function removeProduct($id_product)
    {
        $products = json_decode($this->products, true) ?: [];
        
        $products = array_values(array_filter($products, function($pid) use ($id_product) {
            return $pid != $id_product;
        }));
        
        $this->products = json_encode($products);
        $this->date_upd = date('Y-m-d H:i:s');
        
        return $this->update();
    }

    /**
     * Clear all products from comparison
     *
     * @return bool
     */
    public function clearProducts()
    {
        $this->products = json_encode([]);
        $this->date_upd = date('Y-m-d H:i:s');
        
        return $this->update();
    }

    /**
     * Get product count in comparison
     *
     * @return int
     */
    public function getProductCount()
    {
        $products = json_decode($this->products, true);
        return is_array($products) ? count($products) : 0;
    }

    /**
     * Check if product is in comparison
     *
     * @param int $id_product
     * @return bool
     */
    public function hasProduct($id_product)
    {
        $products = json_decode($this->products, true) ?: [];
        return in_array((int)$id_product, $products);
    }

    /**
     * Create new comparison session
     *
     * @param string $token
     * @param int|null $id_customer
     * @param int|null $id_guest
     * @return StCompareSession|false
     */
    public static function createSession($token, $id_customer = null, $id_guest = null)
    {
        $session = new self();
        $session->session_token = $token;
        $session->id_customer = $id_customer;
        $session->id_guest = $id_guest;
        $session->products = json_encode([]);
        $session->date_add = date('Y-m-d H:i:s');
        $session->date_upd = date('Y-m-d H:i:s');
        
        if ($session->add()) {
            return $session;
        }
        
        return false;
    }

    /**
     * Clean up old sessions
     *
     * @param int $days_old
     * @return bool
     */
    public static function cleanupOldSessions($days_old = 30)
    {
        $date_limit = date('Y-m-d H:i:s', strtotime("-{$days_old} days"));
        
        $sql = 'DELETE FROM `' . _DB_PREFIX_ . 'st_compare_session` 
                WHERE `date_upd` < "' . pSQL($date_limit) . '"';
        
        return Db::getInstance()->execute($sql);
    }

    /**
     * Get comparison statistics
     *
     * @return array
     */
    public static function getStats()
    {
        $stats = [];

        // Total sessions
        $sql = 'SELECT COUNT(*) as total FROM `' . _DB_PREFIX_ . 'st_compare_session`';
        $stats['total_sessions'] = (int)Db::getInstance()->getValue($sql);

        // Active sessions (updated in last 24 hours)
        $sql = 'SELECT COUNT(*) as active FROM `' . _DB_PREFIX_ . 'st_compare_session` 
                WHERE `date_upd` >= DATE_SUB(NOW(), INTERVAL 24 HOUR)';
        $stats['active_sessions'] = (int)Db::getInstance()->getValue($sql);

        // Sessions with products
        $sql = 'SELECT COUNT(*) as with_products FROM `' . _DB_PREFIX_ . 'st_compare_session` 
                WHERE `products` != "[]" AND `products` IS NOT NULL';
        $stats['sessions_with_products'] = (int)Db::getInstance()->getValue($sql);

        // Average products per session
        $sql = 'SELECT AVG(JSON_LENGTH(products)) as avg_products FROM `' . _DB_PREFIX_ . 'st_compare_session` 
                WHERE `products` != "[]" AND `products` IS NOT NULL';
        $stats['avg_products'] = round((float)Db::getInstance()->getValue($sql), 2);

        return $stats;
    }
}
