<?php
/**
 * CompareX - Admin Controller
 * 
 * <AUTHOR>
 * @copyright Copyright (c) 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

class AdminStProductCompareController extends ModuleAdminController
{
    public function __construct()
    {
        $this->table = 'st_compare_session';
        $this->className = 'StCompareSession';
        $this->lang = false;
        $this->bootstrap = true;
        $this->context = Context::getContext();

        parent::__construct();

        $this->meta_title = $this->l('CompareX Management');
        $this->toolbar_title = $this->l('Product Comparison Management');

        // Define list fields
        $this->fields_list = [
            'id_session' => [
                'title' => $this->l('ID'),
                'align' => 'center',
                'class' => 'fixed-width-xs'
            ],
            'customer_info' => [
                'title' => $this->l('Customer'),
                'callback' => 'getCustomerInfo',
                'search' => false,
                'orderby' => false
            ],
            'product_count' => [
                'title' => $this->l('Products'),
                'align' => 'center',
                'callback' => 'getProductCount',
                'search' => false,
                'orderby' => false,
                'class' => 'fixed-width-xs'
            ],
            'product_names' => [
                'title' => $this->l('Product Names'),
                'callback' => 'getProductNames',
                'search' => false,
                'orderby' => false
            ],
            'date_add' => [
                'title' => $this->l('Created'),
                'align' => 'right',
                'type' => 'datetime',
                'class' => 'fixed-width-lg'
            ],
            'date_upd' => [
                'title' => $this->l('Last Updated'),
                'align' => 'right',
                'type' => 'datetime',
                'class' => 'fixed-width-lg'
            ]
        ];

        // Define actions
        $this->actions = ['view', 'delete'];
        $this->bulk_actions = [
            'delete' => [
                'text' => $this->l('Delete selected'),
                'icon' => 'icon-trash',
                'confirm' => $this->l('Delete selected items?')
            ]
        ];

        // Add custom toolbar buttons
        $this->toolbar_btn['stats'] = [
            'href' => self::$currentIndex . '&action=stats&token=' . $this->token,
            'desc' => $this->l('View Statistics'),
            'icon' => 'process-icon-stats'
        ];

        $this->toolbar_btn['cleanup'] = [
            'href' => self::$currentIndex . '&action=cleanup&token=' . $this->token,
            'desc' => $this->l('Cleanup Old Data'),
            'icon' => 'process-icon-eraser',
            'confirm' => $this->l('This will remove comparison sessions older than 30 days. Continue?')
        ];
    }

    public function renderList()
    {
        // Add custom CSS for better display
        $this->addCSS(_MODULE_DIR_ . 'stproductcompare/views/css/admin.css');

        // Add statistics panel before the list
        $stats = $this->getComparisonStats();
        $this->context->smarty->assign([
            'stats' => $stats,
            'module_dir' => _MODULE_DIR_ . 'stproductcompare/'
        ]);

        $stats_panel = $this->context->smarty->fetch(_PS_MODULE_DIR_ . 'stproductcompare/views/templates/admin/stats_panel.tpl');

        return $stats_panel . parent::renderList();
    }

    public function renderView()
    {
        $id_session = (int)Tools::getValue('id_session');
        
        if (!$id_session) {
            $this->errors[] = $this->l('Invalid session ID');
            return $this->renderList();
        }

        $session = $this->getSessionDetails($id_session);
        
        if (!$session) {
            $this->errors[] = $this->l('Session not found');
            return $this->renderList();
        }

        $this->context->smarty->assign([
            'session' => $session,
            'products' => $this->getSessionProducts($session['products']),
            'back_url' => self::$currentIndex . '&token=' . $this->token
        ]);

        return $this->context->smarty->fetch(_PS_MODULE_DIR_ . 'stproductcompare/views/templates/admin/session_view.tpl');
    }

    public function processStats()
    {
        $stats = $this->getDetailedStats();
        
        $this->context->smarty->assign([
            'stats' => $stats,
            'back_url' => self::$currentIndex . '&token=' . $this->token
        ]);

        $this->content = $this->context->smarty->fetch(_PS_MODULE_DIR_ . 'stproductcompare/views/templates/admin/detailed_stats.tpl');
        $this->show_page_header_toolbar = false;
    }

    public function processCleanup()
    {
        $days_old = 30;
        $date_limit = date('Y-m-d H:i:s', strtotime("-{$days_old} days"));
        
        $sql = 'DELETE FROM `' . _DB_PREFIX_ . 'st_compare_session` 
                WHERE `date_upd` < "' . pSQL($date_limit) . '"';
        
        $deleted = Db::getInstance()->execute($sql);
        $affected_rows = Db::getInstance()->Affected_Rows();

        if ($deleted) {
            $this->confirmations[] = sprintf($this->l('Successfully cleaned up %d old comparison sessions'), $affected_rows);
        } else {
            $this->errors[] = $this->l('Error during cleanup process');
        }

        return $this->renderList();
    }

    public function getCustomerInfo($value, $row)
    {
        if ($row['id_customer']) {
            $customer = new Customer($row['id_customer']);
            if (Validate::isLoadedObject($customer)) {
                return $customer->firstname . ' ' . $customer->lastname . ' (' . $customer->email . ')';
            }
        }
        
        return $this->l('Guest') . ' (ID: ' . $row['id_guest'] . ')';
    }

    public function getProductCount($value, $row)
    {
        $products = json_decode($row['products'], true);
        return is_array($products) ? count($products) : 0;
    }

    public function getProductNames($value, $row)
    {
        $products = json_decode($row['products'], true);
        
        if (!is_array($products) || empty($products)) {
            return $this->l('No products');
        }

        $names = [];
        foreach ($products as $id_product) {
            $product = new Product($id_product, false, $this->context->language->id);
            if (Validate::isLoadedObject($product)) {
                $names[] = $product->name;
            }
        }

        if (count($names) > 3) {
            return implode(', ', array_slice($names, 0, 3)) . '... (+' . (count($names) - 3) . ' more)';
        }

        return implode(', ', $names);
    }

    private function getComparisonStats()
    {
        $stats = [];

        // Total sessions
        $sql = 'SELECT COUNT(*) as total FROM `' . _DB_PREFIX_ . 'st_compare_session`';
        $stats['total_sessions'] = (int)Db::getInstance()->getValue($sql);

        // Active sessions (updated in last 24 hours)
        $sql = 'SELECT COUNT(*) as active FROM `' . _DB_PREFIX_ . 'st_compare_session` 
                WHERE `date_upd` >= DATE_SUB(NOW(), INTERVAL 24 HOUR)';
        $stats['active_sessions'] = (int)Db::getInstance()->getValue($sql);

        // Sessions with products
        $sql = 'SELECT COUNT(*) as with_products FROM `' . _DB_PREFIX_ . 'st_compare_session` 
                WHERE `products` != "[]" AND `products` IS NOT NULL';
        $stats['sessions_with_products'] = (int)Db::getInstance()->getValue($sql);

        // Average products per session
        $sql = 'SELECT AVG(JSON_LENGTH(products)) as avg_products FROM `' . _DB_PREFIX_ . 'st_compare_session` 
                WHERE `products` != "[]" AND `products` IS NOT NULL';
        $stats['avg_products'] = round((float)Db::getInstance()->getValue($sql), 2);

        // Most compared products
        $sql = 'SELECT p.id_product, pl.name, COUNT(*) as comparison_count
                FROM `' . _DB_PREFIX_ . 'st_compare_session` s
                JOIN `' . _DB_PREFIX_ . 'product` p ON JSON_CONTAINS(s.products, CAST(p.id_product AS JSON))
                JOIN `' . _DB_PREFIX_ . 'product_lang` pl ON (p.id_product = pl.id_product AND pl.id_lang = ' . (int)$this->context->language->id . ')
                WHERE s.products != "[]"
                GROUP BY p.id_product
                ORDER BY comparison_count DESC
                LIMIT 5';
        
        $stats['top_products'] = Db::getInstance()->executeS($sql) ?: [];

        return $stats;
    }

    private function getDetailedStats()
    {
        $stats = $this->getComparisonStats();

        // Daily comparison activity (last 30 days)
        $sql = 'SELECT DATE(date_add) as date, COUNT(*) as sessions
                FROM `' . _DB_PREFIX_ . 'st_compare_session`
                WHERE date_add >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                GROUP BY DATE(date_add)
                ORDER BY date DESC';
        $stats['daily_activity'] = Db::getInstance()->executeS($sql) ?: [];

        // Customer vs Guest usage
        $sql = 'SELECT 
                    SUM(CASE WHEN id_customer IS NOT NULL THEN 1 ELSE 0 END) as customers,
                    SUM(CASE WHEN id_customer IS NULL THEN 1 ELSE 0 END) as guests
                FROM `' . _DB_PREFIX_ . 'st_compare_session`';
        $usage = Db::getInstance()->getRow($sql);
        $stats['customer_usage'] = $usage ?: ['customers' => 0, 'guests' => 0];

        // Product category comparison frequency
        $sql = 'SELECT c.id_category, cl.name, COUNT(*) as comparison_count
                FROM `' . _DB_PREFIX_ . 'st_compare_session` s
                JOIN `' . _DB_PREFIX_ . 'product` p ON JSON_CONTAINS(s.products, CAST(p.id_product AS JSON))
                JOIN `' . _DB_PREFIX_ . 'category_product` cp ON p.id_product = cp.id_product
                JOIN `' . _DB_PREFIX_ . 'category_lang` cl ON (cp.id_category = cl.id_category AND cl.id_lang = ' . (int)$this->context->language->id . ')
                WHERE s.products != "[]" AND cp.id_category != 1 AND cp.id_category != 2
                GROUP BY c.id_category
                ORDER BY comparison_count DESC
                LIMIT 10';
        $stats['category_stats'] = Db::getInstance()->executeS($sql) ?: [];

        return $stats;
    }

    private function getSessionDetails($id_session)
    {
        $sql = 'SELECT * FROM `' . _DB_PREFIX_ . 'st_compare_session` 
                WHERE id_session = ' . (int)$id_session;
        
        return Db::getInstance()->getRow($sql);
    }

    private function getSessionProducts($products_json)
    {
        $product_ids = json_decode($products_json, true);
        
        if (!is_array($product_ids) || empty($product_ids)) {
            return [];
        }

        $products = [];
        foreach ($product_ids as $id_product) {
            $product = new Product($id_product, false, $this->context->language->id);
            if (Validate::isLoadedObject($product)) {
                $products[] = [
                    'id_product' => $product->id,
                    'name' => $product->name,
                    'reference' => $product->reference,
                    'price' => Product::getPriceStatic($product->id, true),
                    'price_formatted' => Tools::displayPrice(Product::getPriceStatic($product->id, true)),
                    'link' => $this->context->link->getProductLink($product),
                    'image' => $this->context->link->getImageLink($product->link_rewrite, $product->getCoverWs(), ImageType::getFormattedName('small'))
                ];
            }
        }

        return $products;
    }
}
