{*
* CompareX - Debug Buttons Template
* Add this to any page to test CompareX functionality
*}

<div style="position: fixed; top: 10px; right: 10px; z-index: 10000; background: white; padding: 10px; border: 2px solid #007cba; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.2);">
    <h6 style="margin: 0 0 10px 0; color: #007cba;">CompareX Debug</h6>
    <button onclick="testAddToCompare(1)" style="display: block; width: 100%; margin: 5px 0; padding: 8px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer;">
        Add Product 1
    </button>
    <button onclick="testAddToCompare(2)" style="display: block; width: 100%; margin: 5px 0; padding: 8px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">
        Add Product 2
    </button>
    <button onclick="showCompareFooter()" style="display: block; width: 100%; margin: 5px 0; padding: 8px; background: #ffc107; color: black; border: none; border-radius: 4px; cursor: pointer;">
        Show Footer
    </button>
    <button onclick="clearComparison()" style="display: block; width: 100%; margin: 5px 0; padding: 8px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;">
        Clear All
    </button>
    <div style="font-size: 12px; margin-top: 10px; color: #666;">
        Count: <span id="debug-count">0</span>
    </div>
</div>

<script>
// Update debug counter
setInterval(function() {
    var countElement = document.getElementById('st-compare-count');
    var debugCount = document.getElementById('debug-count');
    if (countElement && debugCount) {
        debugCount.textContent = countElement.textContent;
    }
}, 1000);
</script>
