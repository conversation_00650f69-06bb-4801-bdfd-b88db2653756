/**
 * CompareX - Custom Colors CSS
 * 
 * <AUTHOR>
 * @copyright Copyright (c) 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

/* This file is dynamically loaded when custom colors are configured */
/* CSS variables are injected inline by the module */

/* Theme-specific overrides */
.st-compare-theme-dark {
    --st-compare-background: #2c2c2c;
    --st-compare-text: #ffffff;
    --st-compare-border: #444444;
    --st-compare-shadow: rgba(0, 0, 0, 0.3);
    --st-compare-hover: rgba(255, 255, 255, 0.1);
}

.st-compare-theme-dark .st-compare-sticky-footer {
    background: var(--st-compare-background);
    color: var(--st-compare-text);
}

.st-compare-theme-dark .st-compare-product-card {
    background: #3c3c3c;
    border-color: var(--st-compare-border);
    color: var(--st-compare-text);
}

.st-compare-theme-dark .st-compare-table {
    background: var(--st-compare-background);
    color: var(--st-compare-text);
}

.st-compare-theme-dark .st-compare-table .feature-name {
    background: #3c3c3c;
    color: var(--st-compare-text);
}

.st-compare-theme-minimal {
    --st-compare-primary: #000000;
    --st-compare-accent: #666666;
    --st-compare-border: #f0f0f0;
    --st-compare-shadow: rgba(0, 0, 0, 0.05);
}

.st-compare-theme-minimal .st-compare-btn {
    border-radius: 0;
    font-weight: 400;
}

.st-compare-theme-minimal .st-compare-product-card {
    border-radius: 0;
    box-shadow: none;
    border: 1px solid var(--st-compare-border);
}

.st-compare-theme-colorful {
    --st-compare-primary: #ff6b6b;
    --st-compare-accent: #4ecdc4;
    --st-compare-secondary: #45b7d1;
}

.st-compare-theme-colorful .st-compare-btn {
    background: linear-gradient(45deg, var(--st-compare-primary), var(--st-compare-accent));
    border: none;
}

.st-compare-theme-colorful .st-compare-btn:hover {
    background: linear-gradient(45deg, var(--st-compare-accent), var(--st-compare-secondary));
}

.st-compare-theme-colorful .st-compare-sticky-footer {
    background: linear-gradient(90deg, var(--st-compare-primary), var(--st-compare-accent));
}

.st-compare-theme-colorful .st-compare-footer-header {
    background: transparent;
}

/* Custom color overrides */
.st-compare-custom-colors .st-compare-btn {
    background: var(--st-compare-primary);
    color: var(--st-compare-background);
}

.st-compare-custom-colors .st-compare-btn:hover {
    background: var(--st-compare-accent);
}

.st-compare-custom-colors .st-compare-sticky-footer {
    background: var(--st-compare-background);
    border-top-color: var(--st-compare-primary);
}

.st-compare-custom-colors .st-compare-footer-header {
    background: var(--st-compare-primary);
    color: var(--st-compare-background);
}

.st-compare-custom-colors .st-compare-footer-count {
    background: var(--st-compare-accent);
    color: var(--st-compare-background);
}

.st-compare-custom-colors .st-compare-product-card {
    background: var(--st-compare-background);
    border-color: var(--st-compare-border);
    color: var(--st-compare-text);
}

.st-compare-custom-colors .st-compare-table th {
    background: var(--st-compare-primary);
    color: var(--st-compare-background);
}

.st-compare-custom-colors .st-compare-table .feature-name {
    background: var(--st-compare-border);
    color: var(--st-compare-text);
}

.st-compare-custom-colors .st-compare-table .product-price {
    color: var(--st-compare-primary);
}

/* Animation enhancements for themes */
.st-compare-theme-colorful .st-compare-btn {
    transition: all 0.3s ease, background 0.5s ease;
}

.st-compare-theme-colorful .st-compare-product-card:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
}

.st-compare-theme-minimal .st-compare-btn {
    transition: all 0.2s ease;
}

.st-compare-theme-minimal .st-compare-btn:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.st-compare-theme-dark .st-compare-product-card:hover {
    box-shadow: 0 4px 16px rgba(255, 255, 255, 0.1);
}

/* Print styles for all themes */
@media print {
    .st-compare-theme-dark,
    .st-compare-theme-minimal,
    .st-compare-theme-colorful,
    .st-compare-custom-colors {
        --st-compare-background: #ffffff !important;
        --st-compare-text: #000000 !important;
        --st-compare-primary: #000000 !important;
        --st-compare-border: #cccccc !important;
    }
    
    .st-compare-sticky-footer,
    .st-compare-footer-actions,
    .st-compare-filters {
        display: none !important;
    }
}
