{*
* CompareX - Product Button Template
* 
* <AUTHOR>
* @copyright Copyright (c) 2024 ST-themes
* @license   Academic Free License (AFL 3.0)
*}

<div class="st-compare-button-container">
    <button class="st-compare-btn" 
            data-product-id="{$product->id|intval}" 
            title="{l s='Add to comparison' mod='stproductcompare'}">
        {if $st_compare_loading}
            <span class="loading-spinner"></span>
        {/if}
        <i class="fa fa-plus"></i>
        <span class="btn-text">{l s='Add to Compare' mod='stproductcompare'}</span>
    </button>
</div>

<style>
.st-compare-button-container {
    margin: 10px 0;
}

.st-compare-btn.in-comparison {
    background: #28a745;
}

.st-compare-btn.in-comparison:hover {
    background: #218838;
}
</style>
