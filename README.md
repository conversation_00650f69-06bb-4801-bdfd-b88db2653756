# CompareX - Advanced Product Comparison Module

**Revolutionary product comparison module with sticky footer, quick actions, and enhanced user experience for PrestaShop 1.7 to 9.0**

## 🚀 Features

### Sticky Footer
- **Smart Sticky Footer**: Add a sticky footer to your site that shows the products in your product comparison
- **Quick Actions**: Click on products in the sticky footer to display slide-out windows with quick actions (Go to product page, Quick view, Compare now)
- **12 Pre-made Themes**: Choose from 12 beautiful themes or design your own with tons of color options
- **Collapsible Interface**: Toggle the compare tray to collapse it when not needed

### Comparison Page
- **Full Comparison Page**: Brings back the product comparison page for PrestaShop v1.7+
- **Popup Mode**: Show the comparison page in a popup for quicker and easier access
- **Print Functionality**: Add a "Print comparison" button to allow customers to print only the comparison table
- **Feature Filtering**: Optionally hide certain features from being shown in the product comparison table
- **Smart Highlighting**: 
  - Highlight differences between products
  - Highlight similarities between products
  - Hide differences or similarities for cleaner viewing
- **Review Integration**: Display product review ratings if you have a reviews module
- **Hover Effects**: Highlight comparison table rows on hover for easier reading

### Product Integration
- **Product Page Buttons**: Add "Add to compare" button to both product pages and product quick views
- **Product List Integration**: Add compare buttons to product listings
- **Loading Animations**: Display loading icons when products are being added/removed
- **Smart State Management**: Buttons show current comparison status

## 🎨 Themes

CompareX comes with multiple built-in themes:

1. **Default Blue** - Clean, professional blue theme
2. **Dark Mode** - Modern dark interface
3. **Minimal** - Clean, minimalist design
4. **Colorful** - Vibrant, eye-catching colors
5. **Custom Colors** - Design your own with full color customization

## 📋 Requirements

- **PrestaShop**: ******* to 9.0.99
- **PHP**: 7.1 or higher
- **MySQL**: 5.6 or higher

## 🔧 Installation

1. Download the module files
2. Upload to your PrestaShop `/modules/stproductcompare/` directory
3. Go to **Modules > Module Manager** in your PrestaShop admin
4. Find "CompareX - Advanced Product Comparison" and click **Install**
5. Configure the module settings according to your needs

## ⚙️ Configuration

### Basic Settings
- **Enable/Disable**: Turn the module on or off
- **Sticky Footer**: Enable/disable the sticky footer
- **Theme Selection**: Choose from pre-made themes or use custom colors
- **Maximum Products**: Set the maximum number of products that can be compared (default: 4)

### Display Options
- **Popup Mode**: Open comparison in popup window
- **Print Button**: Show/hide print functionality
- **Loading Animations**: Enable/disable loading indicators
- **Review Integration**: Show product reviews in comparison

### Advanced Features
- **Highlight Differences**: Button to highlight product differences
- **Highlight Similarities**: Button to highlight product similarities
- **Hide Options**: Buttons to hide differences or similarities
- **Hover Effects**: Highlight table rows on hover

## 🎯 Usage

### For Customers
1. **Adding Products**: Click "Add to Compare" on any product page or listing
2. **Managing Comparison**: Use the sticky footer to view, remove, or compare products
3. **Viewing Comparison**: Click "Compare Now" to see the full comparison table
4. **Quick Actions**: Use quick view and direct product links from the comparison interface

### For Store Owners
1. **Installation**: Install and activate the module
2. **Configuration**: Set up themes, limits, and display options
3. **Customization**: Use custom colors to match your store's branding
4. **Monitoring**: Track customer engagement with comparison features

## 🔌 Hooks Used

- `displayHeader` - Loads CSS/JS and configuration
- `displayFooterAfter` - Displays sticky footer
- `displayProductButtons` - Shows compare button on product pages
- `displayProductListFunctionalButtons` - Shows compare button in product lists
- `displayQuickView` - Adds compare button to quick view
- `actionFrontControllerSetMedia` - Loads JavaScript configuration

## 🗄️ Database Tables

- `st_product_compare` - Stores individual product comparisons
- `st_compare_session` - Manages comparison sessions and data

## 🎨 Customization

### CSS Customization
The module uses CSS variables for easy theming:

```css
:root {
    --st-compare-primary: #007cba;
    --st-compare-secondary: #666666;
    --st-compare-accent: #ff6b6b;
    --st-compare-background: #ffffff;
    --st-compare-text: #333333;
}
```

### JavaScript Events
The module triggers custom events for integration:

```javascript
// Listen for comparison updates
document.addEventListener('compareX:updated', function(event) {
    console.log('Comparison updated:', event.detail);
});
```

## 🔧 Technical Details

### File Structure
```
stproductcompare/
├── stproductcompare.php          # Main module file
├── config.xml                    # Module configuration
├── controllers/
│   └── front/
│       ├── actions.php           # AJAX actions controller
│       └── compare.php           # Comparison page controller
├── views/
│   ├── css/
│   │   └── front.css            # Frontend styles
│   ├── js/
│   │   └── front.js             # Frontend JavaScript
│   └── templates/
│       ├── hook/                # Hook templates
│       └── front/               # Page templates
└── translations/                # Language files
```

### AJAX Endpoints
- `actions?action=add` - Add product to comparison
- `actions?action=remove` - Remove product from comparison
- `actions?action=clear` - Clear all products
- `actions?action=get` - Get comparison data
- `actions?action=count` - Get product count

## 🌐 Compatibility

- **PrestaShop**: 1.7.0+ to 9.0+
- **PHP**: 7.1, 7.2, 7.3, 7.4, 8.0, 8.1, 8.2
- **Browsers**: Chrome, Firefox, Safari, Edge
- **Mobile**: Fully responsive design
- **Themes**: Compatible with most PrestaShop themes

## 📞 Support

For support, customization, or feature requests:
- **Email**: <EMAIL>
- **Documentation**: Available in module admin panel
- **Updates**: Automatic update notifications

## 📄 License

Academic Free License (AFL 3.0)

## 🏷️ Version

**Version**: 1.0.0  
**Release Date**: 2024  
**Author**: ST-themes

---

**CompareX** - Making product comparison beautiful, fast, and user-friendly! 🚀
