{*
* CompareX - Sticky Footer Template
*
* <AUTHOR>
* @copyright Copyright (c) 2024 ST-themes
* @license   Academic Free License (AFL 3.0)
*}

{* Sticky Footer HTML Structure *}
<!-- CompareX Sticky Footer - Theme: {$st_compare_theme|escape:'html':'UTF-8'} -->
<div class="st-compare-sticky-footer {$st_compare_theme|escape:'html':'UTF-8'}" id="st-compare-footer" style="display: none;">
    <div class="st-compare-footer-header">
        <div class="st-compare-footer-title">
            <i class="fa fa-balance-scale"></i>
            {l s='Product Comparison' mod='stproductcompare'}
            <span class="st-compare-footer-count">0</span>
        </div>
        <button class="st-compare-footer-toggle" title="{l s='Toggle comparison tray' mod='stproductcompare'}">
            <i class="fa fa-chevron-up"></i>
        </button>
    </div>
    <div class="st-compare-footer-content">
        <div class="st-compare-products-grid">
            {* Products will be loaded here dynamically *}
        </div>
        <div class="st-compare-footer-actions">
            <a href="{$compare_link|escape:'html':'UTF-8'}" class="st-compare-action-btn primary st-compare-now">
                <i class="fa fa-balance-scale"></i>
                {l s='Compare Now' mod='stproductcompare'}
            </a>
            <button class="st-compare-action-btn secondary st-compare-clear">
                <i class="fa fa-trash"></i>
                {l s='Clear All' mod='stproductcompare'}
            </button>
        </div>
    </div>
</div>

<script type="text/javascript">
    document.addEventListener('DOMContentLoaded', function() {
        // Configure sticky footer
        if (typeof window.compareX !== 'undefined') {
            window.compareX.stickyFooterConfig = {
                theme: '{$st_compare_theme|escape:'javascript':'UTF-8'}',
                maxProducts: {$st_compare_max_products|intval},
                quickActions: {if $st_compare_quick_actions}true{else}false{/if},
                autoCollapse: {if $st_compare_auto_collapse}true{else}false{/if},
                compareLink: '{$compare_link|escape:'javascript':'UTF-8'}'
            };

            // Load existing comparison data
            window.compareX.loadComparisonData();
        } else {
            // If CompareX is not loaded yet, wait for it
            var checkCompareX = setInterval(function() {
                if (typeof window.compareX !== 'undefined') {
                    clearInterval(checkCompareX);
                    window.compareX.stickyFooterConfig = {
                        theme: '{$st_compare_theme|escape:'javascript':'UTF-8'}',
                        maxProducts: {$st_compare_max_products|intval},
                        quickActions: {if $st_compare_quick_actions}true{else}false{/if},
                        autoCollapse: {if $st_compare_auto_collapse}true{else}false{/if},
                        compareLink: '{$compare_link|escape:'javascript':'UTF-8'}'
                    };
                    window.compareX.loadComparisonData();
                }
            }, 100);
        }
    });
</script>
