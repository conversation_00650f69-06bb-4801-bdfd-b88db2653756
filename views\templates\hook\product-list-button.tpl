{*
* CompareX - Product List Button Template
* 
* <AUTHOR>
* @copyright Copyright (c) 2024 ST-themes
* @license   Academic Free License (AFL 3.0)
*}

<div class="st-compare-list-button">
    <button class="st-compare-btn btn-sm" 
            data-product-id="{$product.id_product|intval}" 
            title="{l s='Add to comparison' mod='stproductcompare'}">
        {if $st_compare_loading}
            <span class="loading-spinner"></span>
        {/if}
        <i class="fa fa-balance-scale"></i>
        <span class="btn-text d-none d-md-inline">{l s='Compare' mod='stproductcompare'}</span>
    </button>
</div>

<style>
.st-compare-list-button {
    display: inline-block;
}

.st-compare-btn.btn-sm {
    padding: 8px 12px;
    font-size: 12px;
}

.st-compare-btn.btn-sm .btn-text {
    margin-left: 4px;
}

@media (max-width: 767px) {
    .st-compare-btn.btn-sm {
        padding: 6px 8px;
    }
    
    .st-compare-btn.btn-sm i {
        font-size: 14px;
    }
}
</style>
