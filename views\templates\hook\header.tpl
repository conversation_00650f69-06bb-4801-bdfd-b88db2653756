{*
* CompareX - Header Template
*
* <AUTHOR>
* @copyright Copyright (c) 2024 ST-themes
* @license   Academic Free License (AFL 3.0)
*}

{* This template is loaded in the header to initialize CompareX *}
<script type="text/javascript">
    // CompareX Configuration
    window.st_compare_config = {
        ajax_url: '{$link->getModuleLink('stproductcompare', 'actions')|escape:'javascript':'UTF-8'}',
        compare_url: '{$link->getModuleLink('stproductcompare', 'compare')|escape:'javascript':'UTF-8'}',
        max_products: {$st_compare_max_products|default:4|intval},
        popup_mode: {if $st_compare_popup_mode}true{else}false{/if},
        loading_animation: {if $st_compare_loading_animation}true{else}false{/if},
        sticky_footer_enabled: {if $st_compare_sticky_footer}true{else}false{/if},
        module_enabled: {if $st_compare_enable}true{else}false{/if},
        theme: '{$st_compare_theme|default:'theme1'|escape:'javascript':'UTF-8'}',
        quick_actions: {if $st_compare_quick_actions}true{else}false{/if},
        auto_collapse: {if $st_compare_auto_collapse}true{else}false{/if}
    };

    // Legacy variables for backward compatibility
    var st_compare_ajax_url = window.st_compare_config.ajax_url;
    var st_compare_max_products = window.st_compare_config.max_products;
    var st_compare_popup_mode = window.st_compare_config.popup_mode;
    var st_compare_loading_animation = window.st_compare_config.loading_animation;
</script>
