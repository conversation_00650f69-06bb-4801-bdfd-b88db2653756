<?php
/**
 * CompareX - Comparison Page Controller
 * 
 * <AUTHOR>
 * @copyright Copyright (c) 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

class StProductCompareCompareModuleFrontController extends ModuleFrontController
{
    public $ssl = true;

    public function initContent()
    {
        parent::initContent();

        $comparison = $this->getComparison();
        $products = json_decode($comparison['products'], true) ?: [];

        if (empty($products)) {
            $this->context->smarty->assign([
                'empty_comparison' => true,
                'message' => $this->module->l('No products to compare. Start adding products to your comparison.')
            ]);
        } else {
            $comparison_data = $this->buildComparisonData($products);
            
            $this->context->smarty->assign([
                'empty_comparison' => false,
                'products' => $comparison_data['products'],
                'features' => $comparison_data['features'],
                'attributes' => $comparison_data['attributes'],
                'show_print' => Configuration::get('ST_COMPARE_SHOW_PRINT'),
                'highlight_differences' => Configuration::get('ST_COMPARE_HIGHLIGHT_DIFFERENCES'),
                'highlight_similarities' => Configuration::get('ST_COMPARE_HIGHLIGHT_SIMILARITIES'),
                'hide_differences' => Configuration::get('ST_COMPARE_HIDE_DIFFERENCES'),
                'hide_similarities' => Configuration::get('ST_COMPARE_HIDE_SIMILARITIES'),
                'show_reviews' => Configuration::get('ST_COMPARE_SHOW_REVIEWS'),
                'hover_highlight' => Configuration::get('ST_COMPARE_HOVER_HIGHLIGHT'),
                'popup_mode' => Configuration::get('ST_COMPARE_POPUP_MODE')
            ]);
        }

        $this->setTemplate('module:stproductcompare/views/templates/front/compare.tpl');
    }

    private function getComparison()
    {
        $session_token = $this->getSessionToken();
        
        $sql = 'SELECT * FROM `' . _DB_PREFIX_ . 'st_compare_session` 
                WHERE `session_token` = "' . pSQL($session_token) . '"';
        
        $comparison = Db::getInstance()->getRow($sql);
        
        if (!$comparison) {
            return ['products' => json_encode([])];
        }
        
        return $comparison;
    }

    private function getSessionToken()
    {
        if (!isset($this->context->cookie->st_compare_token)) {
            $this->context->cookie->st_compare_token = md5(uniqid(rand(), true));
        }
        
        return $this->context->cookie->st_compare_token;
    }

    private function buildComparisonData($product_ids)
    {
        $products = [];
        $all_features = [];
        $all_attributes = [];

        // Load products and collect features/attributes
        foreach ($product_ids as $id_product) {
            $product = new Product($id_product, true, $this->context->language->id);
            if (!Validate::isLoadedObject($product)) {
                continue;
            }

            // Basic product data
            $product_data = [
                'id_product' => $product->id,
                'name' => $product->name,
                'link' => $this->context->link->getProductLink($product),
                'image' => $this->context->link->getImageLink($product->link_rewrite, $product->getCoverWs(), ImageType::getFormattedName('large')),
                'price' => Product::getPriceStatic($product->id, true),
                'price_formatted' => Tools::displayPrice(Product::getPriceStatic($product->id, true)),
                'reference' => $product->reference,
                'description_short' => $product->description_short,
                'weight' => $product->weight,
                'features' => [],
                'attributes' => [],
                'reviews' => []
            ];

            // Get product features
            $features = Product::getFeaturesStatic($id_product);
            foreach ($features as $feature) {
                $feature_name = Feature::getFeature($this->context->language->id, $feature['id_feature']);
                $feature_value = FeatureValue::getFeatureValueLang($feature['id_feature_value']);
                
                if ($feature_name && $feature_value) {
                    $product_data['features'][$feature['id_feature']] = [
                        'name' => $feature_name['name'],
                        'value' => $feature_value[$this->context->language->id]['value']
                    ];
                    
                    $all_features[$feature['id_feature']] = $feature_name['name'];
                }
            }

            // Get product attributes (combinations)
            $combinations = $product->getAttributeCombinations($this->context->language->id);
            $grouped_attributes = [];
            
            foreach ($combinations as $combination) {
                if (!isset($grouped_attributes[$combination['id_attribute_group']])) {
                    $grouped_attributes[$combination['id_attribute_group']] = [
                        'name' => $combination['group_name'],
                        'values' => []
                    ];
                }
                $grouped_attributes[$combination['id_attribute_group']]['values'][] = $combination['attribute_name'];
            }

            foreach ($grouped_attributes as $id_group => $group_data) {
                $product_data['attributes'][$id_group] = [
                    'name' => $group_data['name'],
                    'values' => array_unique($group_data['values'])
                ];
                
                $all_attributes[$id_group] = $group_data['name'];
            }

            // Get reviews if enabled and hook exists
            if (Configuration::get('ST_COMPARE_SHOW_REVIEWS')) {
                $reviews_data = Hook::exec('displayProductListReviews', ['product' => $product]);
                if ($reviews_data) {
                    $product_data['reviews'] = $reviews_data;
                }
            }

            $products[] = $product_data;
        }

        return [
            'products' => $products,
            'features' => $all_features,
            'attributes' => $all_attributes
        ];
    }

    public function getBreadcrumbLinks()
    {
        $breadcrumb = parent::getBreadcrumbLinks();
        
        $breadcrumb['links'][] = [
            'title' => $this->module->l('Product Comparison'),
            'url' => $this->context->link->getModuleLink($this->module->name, 'compare')
        ];

        return $breadcrumb;
    }

    public function getCanonicalURL()
    {
        return $this->context->link->getModuleLink($this->module->name, 'compare');
    }
}
