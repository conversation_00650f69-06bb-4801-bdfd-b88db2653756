{*
* CompareX - Test Page Template
* 
* <AUTHOR>
* @copyright Copyright (c) 2024 ST-themes
* @license   Academic Free License (AFL 3.0)
*}

{extends file='page.tpl'}

{block name='page_title'}
    CompareX Test Page
{/block}

{block name='page_content'}
    <div class="st-compare-test-page">
        <div class="alert alert-info">
            <h4><i class="fa fa-info-circle"></i> CompareX Module Test</h4>
            <p>This page helps you test the CompareX functionality.</p>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fa fa-cog"></i> Module Configuration</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between">
                                <span>Module Enabled:</span>
                                <span class="badge badge-{if $module_enabled}success{else}danger{/if}">
                                    {if $module_enabled}Yes{else}No{/if}
                                </span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span>Sticky Footer:</span>
                                <span class="badge badge-{if $sticky_footer_enabled}success{else}danger{/if}">
                                    {if $sticky_footer_enabled}Enabled{else}Disabled{/if}
                                </span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span>Theme:</span>
                                <span class="badge badge-info">{$theme|default:'Not Set'}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span>Max Products:</span>
                                <span class="badge badge-secondary">{$max_products|default:'Not Set'}</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fa fa-flask"></i> Test Actions</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary btn-block mb-2" onclick="testAddProduct(1)">
                            <i class="fa fa-plus"></i> Test Add Product 1
                        </button>
                        <button class="btn btn-primary btn-block mb-2" onclick="testAddProduct(2)">
                            <i class="fa fa-plus"></i> Test Add Product 2
                        </button>
                        <button class="btn btn-warning btn-block mb-2" onclick="testRemoveProduct(1)">
                            <i class="fa fa-minus"></i> Test Remove Product 1
                        </button>
                        <button class="btn btn-danger btn-block mb-2" onclick="testClearComparison()">
                            <i class="fa fa-trash"></i> Test Clear All
                        </button>
                        <button class="btn btn-info btn-block" onclick="testGetComparison()">
                            <i class="fa fa-list"></i> Test Get Comparison
                        </button>
                    </div>
                </div>
            </div>
        </div>

        {if $test_products && count($test_products) > 0}
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fa fa-shopping-cart"></i> Sample Products for Testing</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                {foreach from=$test_products item=product}
                                    <div class="col-md-4 mb-3">
                                        <div class="card">
                                            <div class="card-body text-center">
                                                <h6 class="card-title">{$product.name|escape:'html':'UTF-8'}</h6>
                                                <p class="card-text">{$product.price}</p>
                                                <button class="st-compare-btn" data-product-id="{$product.id_product}">
                                                    <i class="fa fa-plus"></i>
                                                    <span class="btn-text">Add to Compare</span>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                {/foreach}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {/if}

        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fa fa-terminal"></i> Debug Console</h5>
                    </div>
                    <div class="card-body">
                        <div id="debug-console" style="background: #f8f9fa; padding: 15px; border-radius: 4px; font-family: monospace; min-height: 200px; overflow-y: auto;">
                            <div>CompareX Debug Console - Ready</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function debugLog(message) {
            const console = document.getElementById('debug-console');
            const timestamp = new Date().toLocaleTimeString();
            console.innerHTML += '<div>[' + timestamp + '] ' + message + '</div>';
            console.scrollTop = console.scrollHeight;
        }

        function testAddProduct(productId) {
            debugLog('Testing add product ' + productId + '...');
            
            if (typeof window.compareX !== 'undefined') {
                window.compareX.addProduct(productId);
                debugLog('Add product request sent');
            } else {
                debugLog('ERROR: CompareX not loaded');
            }
        }

        function testRemoveProduct(productId) {
            debugLog('Testing remove product ' + productId + '...');
            
            if (typeof window.compareX !== 'undefined') {
                window.compareX.removeProduct(productId);
                debugLog('Remove product request sent');
            } else {
                debugLog('ERROR: CompareX not loaded');
            }
        }

        function testClearComparison() {
            debugLog('Testing clear comparison...');
            
            if (typeof window.compareX !== 'undefined') {
                window.compareX.clearComparison();
                debugLog('Clear comparison request sent');
            } else {
                debugLog('ERROR: CompareX not loaded');
            }
        }

        function testGetComparison() {
            debugLog('Testing get comparison...');
            
            if (typeof window.compareX !== 'undefined') {
                window.compareX.loadComparisonData();
                debugLog('Get comparison request sent');
            } else {
                debugLog('ERROR: CompareX not loaded');
            }
        }

        // Check if CompareX is loaded
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                if (typeof window.compareX !== 'undefined') {
                    debugLog('SUCCESS: CompareX is loaded and ready');
                    debugLog('AJAX URL: ' + window.compareX.ajaxUrl);
                    debugLog('Max Products: ' + window.compareX.maxProducts);
                } else {
                    debugLog('WARNING: CompareX not found - checking if scripts are loaded...');
                    
                    // Check if jQuery is loaded
                    if (typeof $ !== 'undefined') {
                        debugLog('jQuery is loaded');
                    } else {
                        debugLog('ERROR: jQuery not loaded');
                    }
                }
                
                // Check if sticky footer exists
                if ($('#st-compare-footer').length > 0) {
                    debugLog('SUCCESS: Sticky footer HTML found');
                } else {
                    debugLog('WARNING: Sticky footer HTML not found');
                }
            }, 1000);
        });
    </script>
{/block}
