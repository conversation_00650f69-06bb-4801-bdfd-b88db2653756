{*
* CompareX - Session View Template
* 
* <AUTHOR>
* @copyright Copyright (c) 2024 ST-themes
* @license   Academic Free License (AFL 3.0)
*}

<div class="st-compare-session-view">
    <div class="st-compare-session-header">
        <h2>
            <i class="icon-eye"></i>
            {l s='Comparison Session Details' mod='stproductcompare'} #{$session.id_session|intval}
        </h2>
        <a href="{$back_url|escape:'html':'UTF-8'}" class="btn btn-default">
            <i class="icon-arrow-left"></i>
            {l s='Back to List' mod='stproductcompare'}
        </a>
    </div>

    <div class="st-compare-session-info">
        <div class="st-compare-info-group">
            <div class="st-compare-info-label">{l s='Session ID' mod='stproductcompare'}</div>
            <div class="st-compare-info-value">{$session.id_session|intval}</div>
        </div>
        
        <div class="st-compare-info-group">
            <div class="st-compare-info-label">{l s='Customer' mod='stproductcompare'}</div>
            <div class="st-compare-info-value">
                {if $session.id_customer}
                    {l s='Customer ID:' mod='stproductcompare'} {$session.id_customer|intval}
                {else}
                    {l s='Guest' mod='stproductcompare'}
                    <br><small>Guest ID: {$session.id_guest|intval}</small>
                {/if}
            </div>
        </div>
        
        <div class="st-compare-info-group">
            <div class="st-compare-info-label">{l s='Session Token' mod='stproductcompare'}</div>
            <div class="st-compare-info-value">
                <code>{$session.session_token|escape:'html':'UTF-8'}</code>
            </div>
        </div>
        
        <div class="st-compare-info-group">
            <div class="st-compare-info-label">{l s='Created' mod='stproductcompare'}</div>
            <div class="st-compare-info-value">{$session.date_add|date_format:'%Y-%m-%d %H:%M:%S'}</div>
        </div>
        
        <div class="st-compare-info-group">
            <div class="st-compare-info-label">{l s='Last Updated' mod='stproductcompare'}</div>
            <div class="st-compare-info-value">{$session.date_upd|date_format:'%Y-%m-%d %H:%M:%S'}</div>
        </div>
        
        <div class="st-compare-info-group">
            <div class="st-compare-info-label">{l s='Products Count' mod='stproductcompare'}</div>
            <div class="st-compare-info-value">{$products|count}</div>
        </div>
    </div>

    {if $products && count($products) > 0}
        <h3>
            <i class="icon-list"></i>
            {l s='Products in Comparison' mod='stproductcompare'}
        </h3>
        
        <div class="st-compare-products-grid">
            {foreach from=$products item=product}
                <div class="st-compare-product-card">
                    <img src="{$product.image|escape:'html':'UTF-8'}" 
                         alt="{$product.name|escape:'html':'UTF-8'}" 
                         class="st-compare-product-image">
                    
                    <div class="st-compare-product-title">
                        {$product.name|escape:'html':'UTF-8'}
                    </div>
                    
                    {if $product.reference}
                        <div class="st-compare-product-ref">
                            {l s='Ref:' mod='stproductcompare'} {$product.reference|escape:'html':'UTF-8'}
                        </div>
                    {/if}
                    
                    <div class="st-compare-product-price">
                        {$product.price_formatted|escape:'html':'UTF-8'}
                    </div>
                    
                    <a href="{$product.link|escape:'html':'UTF-8'}" 
                       target="_blank" 
                       class="st-compare-product-link">
                        <i class="icon-external-link"></i>
                        {l s='View Product' mod='stproductcompare'}
                    </a>
                </div>
            {/foreach}
        </div>
    {else}
        <div class="alert alert-info">
            <i class="icon-info"></i>
            {l s='No products in this comparison session.' mod='stproductcompare'}
        </div>
    {/if}
</div>
