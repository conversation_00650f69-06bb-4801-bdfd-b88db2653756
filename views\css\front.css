/**
 * CompareX - Frontend Styles
 * 
 * <AUTHOR>
 * @copyright Copyright (c) 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

/* CSS Variables for theming */
:root {
    --st-compare-primary: #007cba;
    --st-compare-secondary: #666666;
    --st-compare-accent: #ff6b6b;
    --st-compare-background: #ffffff;
    --st-compare-text: #333333;
    --st-compare-border: #e0e0e0;
    --st-compare-shadow: rgba(0, 0, 0, 0.1);
    --st-compare-hover: rgba(0, 124, 186, 0.1);
}

/* Compare Button Styles */
.st-compare-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    background: var(--st-compare-primary);
    color: white;
    border: none;
    border-radius: 4px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.st-compare-btn:hover {
    background: var(--st-compare-accent);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px var(--st-compare-shadow);
}

.st-compare-btn:active {
    transform: translateY(0);
}

.st-compare-btn.loading {
    pointer-events: none;
    opacity: 0.7;
}

.st-compare-btn .loading-spinner {
    display: none;
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.st-compare-btn.loading .loading-spinner {
    display: block;
}

.st-compare-btn.loading .btn-text {
    display: none;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Sticky Footer Styles */
.st-compare-sticky-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--st-compare-background);
    border-top: 2px solid var(--st-compare-primary);
    box-shadow: 0 -4px 20px var(--st-compare-shadow);
    z-index: 1000;
    transform: translateY(100%);
    transition: transform 0.3s ease;
    display: none;
}

.st-compare-sticky-footer.visible {
    transform: translateY(0);
    display: block !important;
}

.st-compare-sticky-footer.collapsed {
    transform: translateY(calc(100% - 50px));
}

.st-compare-footer-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 20px;
    background: var(--st-compare-primary);
    color: white;
    cursor: pointer;
}

.st-compare-footer-title {
    font-weight: 600;
    font-size: 16px;
}

.st-compare-footer-count {
    background: var(--st-compare-accent);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    min-width: 20px;
    text-align: center;
}

.st-compare-footer-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background 0.2s ease;
}

.st-compare-footer-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
}

.st-compare-footer-content {
    padding: 20px;
    max-height: 300px;
    overflow-y: auto;
}

.st-compare-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 20px;
}

.st-compare-product-card {
    background: white;
    border: 1px solid var(--st-compare-border);
    border-radius: 8px;
    padding: 16px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
}

.st-compare-product-card:hover {
    box-shadow: 0 4px 16px var(--st-compare-shadow);
    transform: translateY(-2px);
}

.st-compare-product-image {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 4px;
    margin-bottom: 12px;
}

.st-compare-product-name {
    font-size: 14px;
    font-weight: 500;
    color: var(--st-compare-text);
    margin-bottom: 8px;
    line-height: 1.3;
}

.st-compare-product-price {
    font-size: 16px;
    font-weight: 600;
    color: var(--st-compare-primary);
    margin-bottom: 12px;
}

.st-compare-product-actions {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.st-compare-product-action {
    padding: 6px 12px;
    border: 1px solid var(--st-compare-border);
    background: white;
    color: var(--st-compare-text);
    text-decoration: none;
    border-radius: 4px;
    font-size: 12px;
    transition: all 0.2s ease;
}

.st-compare-product-action:hover {
    background: var(--st-compare-primary);
    color: white;
    border-color: var(--st-compare-primary);
}

.st-compare-product-remove {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 24px;
    height: 24px;
    background: var(--st-compare-accent);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.st-compare-product-remove:hover {
    background: #e55555;
    transform: scale(1.1);
}

.st-compare-footer-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    padding-top: 16px;
    border-top: 1px solid var(--st-compare-border);
}

.st-compare-action-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.st-compare-action-btn.primary {
    background: var(--st-compare-primary);
    color: white;
}

.st-compare-action-btn.primary:hover {
    background: var(--st-compare-accent);
    transform: translateY(-1px);
}

.st-compare-action-btn.secondary {
    background: var(--st-compare-border);
    color: var(--st-compare-text);
}

.st-compare-action-btn.secondary:hover {
    background: var(--st-compare-secondary);
    color: white;
}

/* Comparison Table Styles */
.st-compare-table-container {
    overflow-x: auto;
    margin: 20px 0;
}

.st-compare-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 16px var(--st-compare-shadow);
}

.st-compare-table th,
.st-compare-table td {
    padding: 16px;
    text-align: left;
    border-bottom: 1px solid var(--st-compare-border);
}

.st-compare-table th {
    background: var(--st-compare-primary);
    color: white;
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 10;
}

.st-compare-table tr:hover {
    background: var(--st-compare-hover);
}

.st-compare-table .product-header {
    text-align: center;
    padding: 24px 16px;
}

.st-compare-table .product-image {
    width: 120px;
    height: 120px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 12px;
}

.st-compare-table .product-name {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
}

.st-compare-table .product-price {
    font-size: 18px;
    font-weight: 700;
    color: var(--st-compare-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
    .st-compare-products-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 12px;
    }
    
    .st-compare-product-card {
        padding: 12px;
    }
    
    .st-compare-product-image {
        width: 60px;
        height: 60px;
    }
    
    .st-compare-footer-content {
        padding: 16px;
    }
    
    .st-compare-footer-actions {
        flex-direction: column;
    }
    
    .st-compare-action-btn {
        justify-content: center;
    }
}

/* Theme Variations */
.st-compare-theme-dark {
    --st-compare-background: #2c2c2c;
    --st-compare-text: #ffffff;
    --st-compare-border: #444444;
    --st-compare-shadow: rgba(0, 0, 0, 0.3);
}

.st-compare-theme-minimal {
    --st-compare-primary: #000000;
    --st-compare-accent: #666666;
    --st-compare-border: #f0f0f0;
}

.st-compare-theme-colorful {
    --st-compare-primary: #ff6b6b;
    --st-compare-accent: #4ecdc4;
    --st-compare-secondary: #45b7d1;
}

/* Animation Classes */
.st-compare-fade-in {
    animation: fadeIn 0.3s ease;
}

.st-compare-slide-up {
    animation: slideUp 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}
