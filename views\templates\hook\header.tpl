{*
* CompareX - Header Template
* 
* <AUTHOR>
* @copyright Copyright (c) 2024 ST-themes
* @license   Academic Free License (AFL 3.0)
*}

{* This template is loaded in the header to initialize CompareX *}
<script type="text/javascript">
    var st_compare_ajax_url = '{$link->getModuleLink('stproductcompare', 'actions')|escape:'javascript':'UTF-8'}';
    var st_compare_max_products = {$st_compare_max_products|default:4|intval};
    var st_compare_popup_mode = {if $st_compare_popup_mode}true{else}false{/if};
    var st_compare_loading_animation = {if $st_compare_loading_animation}true{else}false{/if};
</script>
