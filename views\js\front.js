/**
 * CompareX - Frontend JavaScript
 * 
 * <AUTHOR>
 * @copyright Copyright (c) 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

class CompareX {
    constructor() {
        this.maxProducts = window.st_compare_max_products || 4;
        this.ajaxUrl = window.st_compare_ajax_url || '';
        this.popupMode = window.st_compare_popup_mode || false;
        this.loadingAnimation = window.st_compare_loading_animation || true;
        this.currentCount = 0;
        this.products = [];
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadComparisonData();
        this.createStickyFooter();
    }

    bindEvents() {
        // Add to compare buttons
        $(document).on('click', '.st-compare-btn', (e) => {
            e.preventDefault();
            const $btn = $(e.currentTarget);
            const productId = $btn.data('product-id');
            
            if (productId) {
                this.addProduct(productId, $btn);
            }
        });

        // Remove from compare buttons
        $(document).on('click', '.st-compare-remove', (e) => {
            e.preventDefault();
            e.stopPropagation();
            const productId = $(e.currentTarget).data('product-id');
            
            if (productId) {
                this.removeProduct(productId);
            }
        });

        // Clear comparison
        $(document).on('click', '.st-compare-clear', (e) => {
            e.preventDefault();
            this.clearComparison();
        });

        // Toggle sticky footer
        $(document).on('click', '.st-compare-footer-toggle', (e) => {
            e.preventDefault();
            this.toggleStickyFooter();
        });

        // Product quick actions
        $(document).on('click', '.st-compare-product-card', (e) => {
            if (!$(e.target).hasClass('st-compare-remove') && 
                !$(e.target).hasClass('st-compare-product-action')) {
                this.showProductQuickActions($(e.currentTarget));
            }
        });

        // Compare now button
        $(document).on('click', '.st-compare-now', (e) => {
            e.preventDefault();
            this.openComparison();
        });

        // Print comparison
        $(document).on('click', '.st-compare-print', (e) => {
            e.preventDefault();
            this.printComparison();
        });
    }

    addProduct(productId, $btn) {
        if (this.currentCount >= this.maxProducts) {
            this.showMessage(`Maximum ${this.maxProducts} products allowed in comparison`, 'error');
            return;
        }

        if (this.loadingAnimation && $btn) {
            this.setButtonLoading($btn, true);
        }

        $.ajax({
            url: this.ajaxUrl,
            type: 'POST',
            data: {
                action: 'add',
                id_product: productId
            },
            dataType: 'json',
            success: (response) => {
                if (response.success) {
                    this.currentCount = response.count;
                    this.products = response.products || [];
                    this.updateStickyFooter();
                    this.showMessage(response.message, 'success');
                    
                    if ($btn) {
                        this.updateButtonState($btn, true);
                    }
                } else {
                    this.showMessage(response.message, 'error');
                }
            },
            error: () => {
                this.showMessage('Error adding product to comparison', 'error');
            },
            complete: () => {
                if (this.loadingAnimation && $btn) {
                    this.setButtonLoading($btn, false);
                }
            }
        });
    }

    removeProduct(productId) {
        $.ajax({
            url: this.ajaxUrl,
            type: 'POST',
            data: {
                action: 'remove',
                id_product: productId
            },
            dataType: 'json',
            success: (response) => {
                if (response.success) {
                    this.currentCount = response.count;
                    this.products = response.products || [];
                    this.updateStickyFooter();
                    this.showMessage(response.message, 'success');
                    
                    // Update button states
                    $(`.st-compare-btn[data-product-id="${productId}"]`).each((i, btn) => {
                        this.updateButtonState($(btn), false);
                    });
                } else {
                    this.showMessage(response.message, 'error');
                }
            },
            error: () => {
                this.showMessage('Error removing product from comparison', 'error');
            }
        });
    }

    clearComparison() {
        if (!confirm('Are you sure you want to clear all products from comparison?')) {
            return;
        }

        $.ajax({
            url: this.ajaxUrl,
            type: 'POST',
            data: {
                action: 'clear'
            },
            dataType: 'json',
            success: (response) => {
                if (response.success) {
                    this.currentCount = 0;
                    this.products = [];
                    this.updateStickyFooter();
                    this.showMessage(response.message, 'success');
                    
                    // Reset all button states
                    $('.st-compare-btn').each((i, btn) => {
                        this.updateButtonState($(btn), false);
                    });
                }
            },
            error: () => {
                this.showMessage('Error clearing comparison', 'error');
            }
        });
    }

    loadComparisonData() {
        $.ajax({
            url: this.ajaxUrl,
            type: 'POST',
            data: {
                action: 'get'
            },
            dataType: 'json',
            success: (response) => {
                if (response.success) {
                    this.currentCount = response.count;
                    this.products = response.products || [];
                    this.updateStickyFooter();
                    this.updateButtonStates();
                }
            }
        });
    }

    createStickyFooter() {
        if ($('.st-compare-sticky-footer').length > 0) {
            return;
        }

        const footerHtml = `
            <div class="st-compare-sticky-footer" id="st-compare-footer">
                <div class="st-compare-footer-header">
                    <div class="st-compare-footer-title">
                        Product Comparison
                        <span class="st-compare-footer-count">0</span>
                    </div>
                    <button class="st-compare-footer-toggle" title="Toggle comparison tray">
                        <i class="fa fa-chevron-up"></i>
                    </button>
                </div>
                <div class="st-compare-footer-content">
                    <div class="st-compare-products-grid"></div>
                    <div class="st-compare-footer-actions">
                        <a href="#" class="st-compare-action-btn primary st-compare-now">
                            <i class="fa fa-balance-scale"></i>
                            Compare Now
                        </a>
                        <button class="st-compare-action-btn secondary st-compare-clear">
                            <i class="fa fa-trash"></i>
                            Clear All
                        </button>
                    </div>
                </div>
            </div>
        `;

        $('body').append(footerHtml);
    }

    updateStickyFooter() {
        const $footer = $('#st-compare-footer');
        const $count = $footer.find('.st-compare-footer-count');
        const $grid = $footer.find('.st-compare-products-grid');

        $count.text(this.currentCount);

        if (this.currentCount > 0) {
            $footer.addClass('visible');
            this.renderProductGrid($grid);
        } else {
            $footer.removeClass('visible');
        }
    }

    renderProductGrid($container) {
        $container.empty();

        this.products.forEach(product => {
            const productHtml = `
                <div class="st-compare-product-card" data-product-id="${product.id_product}">
                    <button class="st-compare-product-remove" data-product-id="${product.id_product}" title="Remove from comparison">
                        <i class="fa fa-times"></i>
                    </button>
                    <img src="${product.image}" alt="${product.name}" class="st-compare-product-image">
                    <div class="st-compare-product-name">${product.name}</div>
                    <div class="st-compare-product-price">${product.price_formatted}</div>
                    <div class="st-compare-product-actions">
                        <a href="${product.link}" class="st-compare-product-action" title="View product">
                            <i class="fa fa-eye"></i>
                        </a>
                        <a href="#" class="st-compare-product-action st-compare-quick-view" data-product-id="${product.id_product}" title="Quick view">
                            <i class="fa fa-search-plus"></i>
                        </a>
                    </div>
                </div>
            `;
            $container.append(productHtml);
        });
    }

    toggleStickyFooter() {
        const $footer = $('#st-compare-footer');
        const $toggle = $footer.find('.st-compare-footer-toggle i');
        
        $footer.toggleClass('collapsed');
        
        if ($footer.hasClass('collapsed')) {
            $toggle.removeClass('fa-chevron-up').addClass('fa-chevron-down');
        } else {
            $toggle.removeClass('fa-chevron-down').addClass('fa-chevron-up');
        }
    }

    openComparison() {
        if (this.currentCount === 0) {
            this.showMessage('No products to compare', 'error');
            return;
        }

        const compareUrl = this.ajaxUrl.replace('/actions', '/compare');
        
        if (this.popupMode) {
            this.openComparisonPopup(compareUrl);
        } else {
            window.location.href = compareUrl;
        }
    }

    openComparisonPopup(url) {
        const popup = window.open(
            url,
            'comparePopup',
            'width=1200,height=800,scrollbars=yes,resizable=yes,toolbar=no,menubar=no'
        );
        
        if (popup) {
            popup.focus();
        }
    }

    printComparison() {
        if (this.currentCount === 0) {
            this.showMessage('No products to compare', 'error');
            return;
        }

        const compareUrl = this.ajaxUrl.replace('/actions', '/compare') + '?print=1';
        
        const printWindow = window.open(compareUrl, 'printWindow', 'width=800,height=600');
        
        if (printWindow) {
            printWindow.onload = function() {
                printWindow.print();
                printWindow.close();
            };
        }
    }

    setButtonLoading($btn, loading) {
        if (loading) {
            $btn.addClass('loading').prop('disabled', true);
        } else {
            $btn.removeClass('loading').prop('disabled', false);
        }
    }

    updateButtonState($btn, inComparison) {
        const $text = $btn.find('.btn-text');
        const $icon = $btn.find('i');
        
        if (inComparison) {
            $text.text('In Comparison');
            $icon.removeClass('fa-plus').addClass('fa-check');
            $btn.addClass('in-comparison');
        } else {
            $text.text('Add to Compare');
            $icon.removeClass('fa-check').addClass('fa-plus');
            $btn.removeClass('in-comparison');
        }
    }

    updateButtonStates() {
        const productIds = this.products.map(p => p.id_product);
        
        $('.st-compare-btn').each((i, btn) => {
            const $btn = $(btn);
            const productId = parseInt($btn.data('product-id'));
            const inComparison = productIds.includes(productId);
            
            this.updateButtonState($btn, inComparison);
        });
    }

    showMessage(message, type = 'info') {
        // Create or update notification
        let $notification = $('#st-compare-notification');
        
        if ($notification.length === 0) {
            $notification = $(`
                <div id="st-compare-notification" class="st-compare-notification">
                    <span class="message"></span>
                    <button class="close">&times;</button>
                </div>
            `);
            $('body').append($notification);
        }

        $notification
            .removeClass('success error info warning')
            .addClass(type)
            .find('.message')
            .text(message);

        $notification.addClass('show');

        // Auto hide after 3 seconds
        setTimeout(() => {
            $notification.removeClass('show');
        }, 3000);

        // Close button
        $notification.find('.close').off('click').on('click', () => {
            $notification.removeClass('show');
        });
    }
}

// Initialize CompareX when document is ready
$(document).ready(() => {
    window.compareX = new CompareX();
});

// Add notification styles
const notificationCSS = `
    .st-compare-notification {
        position: fixed;
        top: 20px;
        right: 20px;
        background: white;
        border-left: 4px solid #007cba;
        padding: 16px 20px;
        border-radius: 4px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        max-width: 300px;
        display: flex;
        align-items: center;
        gap: 12px;
    }
    
    .st-compare-notification.show {
        transform: translateX(0);
    }
    
    .st-compare-notification.success {
        border-left-color: #28a745;
    }
    
    .st-compare-notification.error {
        border-left-color: #dc3545;
    }
    
    .st-compare-notification.warning {
        border-left-color: #ffc107;
    }
    
    .st-compare-notification .message {
        flex: 1;
        font-size: 14px;
        color: #333;
    }
    
    .st-compare-notification .close {
        background: none;
        border: none;
        font-size: 18px;
        color: #999;
        cursor: pointer;
        padding: 0;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .st-compare-notification .close:hover {
        color: #333;
    }
`;

// Inject notification CSS
const style = document.createElement('style');
style.textContent = notificationCSS;
document.head.appendChild(style);
