<?php
/**
 * CompareX - Advanced Product Comparison Module
 * 
 * <AUTHOR>
 * @copyright Copyright (c) 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 * @version   1.0.0
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

// Include module classes
require_once dirname(__FILE__) . '/classes/StCompareSession.php';

class StProductCompare extends Module
{
    public function __construct()
    {
        $this->name = 'stproductcompare';
        $this->tab = 'front_office_features';
        $this->version = '1.0.0';
        $this->author = 'ST-themes';
        $this->need_instance = 0;
        $this->ps_versions_compliancy = [
            'min' => '*******',
            'max' => '9.0.99'
        ];
        $this->bootstrap = true;

        parent::__construct();

        $this->displayName = $this->l('CompareX - Advanced Product Comparison');
        $this->description = $this->l('Revolutionary product comparison module with sticky footer, quick actions, and enhanced user experience for PrestaShop 1.7 to 9.0');
        $this->confirmUninstall = $this->l('Are you sure you want to uninstall CompareX? All comparison data will be lost.');
    }

    public function install()
    {
        return parent::install()
            && $this->installDb()
            && $this->installTab()
            && $this->registerHook('displayHeader')
            && $this->registerHook('displayFooterAfter')
            && $this->registerHook('displayFooter')
            && $this->registerHook('displayProductButtons')
            && $this->registerHook('displayProductListFunctionalButtons')
            && $this->registerHook('displayQuickView')
            && $this->registerHook('actionFrontControllerSetMedia')
            && $this->createDefaultConfiguration();
    }

    public function uninstall()
    {
        return parent::uninstall()
            && $this->uninstallDb()
            && $this->uninstallTab()
            && $this->deleteConfiguration();
    }

    private function installDb()
    {
        $sql = [];

        // Table for storing product comparisons
        $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'st_product_compare` (
            `id_compare` int(10) unsigned NOT NULL AUTO_INCREMENT,
            `id_customer` int(10) unsigned DEFAULT NULL,
            `id_guest` int(10) unsigned DEFAULT NULL,
            `id_product` int(10) unsigned NOT NULL,
            `date_add` datetime NOT NULL,
            PRIMARY KEY (`id_compare`),
            KEY `id_customer` (`id_customer`),
            KEY `id_guest` (`id_guest`),
            KEY `id_product` (`id_product`)
        ) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

        // Table for comparison sessions
        $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'st_compare_session` (
            `id_session` int(10) unsigned NOT NULL AUTO_INCREMENT,
            `session_token` varchar(64) NOT NULL,
            `id_customer` int(10) unsigned DEFAULT NULL,
            `id_guest` int(10) unsigned DEFAULT NULL,
            `products` text,
            `date_add` datetime NOT NULL,
            `date_upd` datetime NOT NULL,
            PRIMARY KEY (`id_session`),
            UNIQUE KEY `session_token` (`session_token`),
            KEY `id_customer` (`id_customer`),
            KEY `id_guest` (`id_guest`)
        ) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

        foreach ($sql as $query) {
            if (!Db::getInstance()->execute($query)) {
                return false;
            }
        }

        return true;
    }

    private function uninstallDb()
    {
        $sql = [
            'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'st_product_compare`',
            'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'st_compare_session`'
        ];

        foreach ($sql as $query) {
            if (!Db::getInstance()->execute($query)) {
                return false;
            }
        }

        return true;
    }

    private function installTab()
    {
        $tab = new Tab();
        $tab->active = 1;
        $tab->class_name = 'AdminStProductCompare';
        $tab->name = [];

        foreach (Language::getLanguages(true) as $lang) {
            $tab->name[$lang['id_lang']] = 'CompareX Management';
        }

        $tab->id_parent = (int)Tab::getIdFromClassName('AdminCatalog');
        $tab->module = $this->name;

        return $tab->add();
    }

    private function uninstallTab()
    {
        $id_tab = (int)Tab::getIdFromClassName('AdminStProductCompare');

        if ($id_tab) {
            $tab = new Tab($id_tab);
            return $tab->delete();
        }

        return true;
    }

    private function createDefaultConfiguration()
    {
        $config = [
            'ST_COMPARE_ENABLE' => 1,
            'ST_COMPARE_STICKY_FOOTER' => 1,
            'ST_COMPARE_THEME' => 'theme1',
            'ST_COMPARE_MAX_PRODUCTS' => 4,
            'ST_COMPARE_POPUP_MODE' => 1,
            'ST_COMPARE_SHOW_PRINT' => 1,
            'ST_COMPARE_HIGHLIGHT_DIFFERENCES' => 1,
            'ST_COMPARE_HIGHLIGHT_SIMILARITIES' => 0,
            'ST_COMPARE_HIDE_DIFFERENCES' => 0,
            'ST_COMPARE_HIDE_SIMILARITIES' => 0,
            'ST_COMPARE_SHOW_REVIEWS' => 1,
            'ST_COMPARE_HOVER_HIGHLIGHT' => 1,
            'ST_COMPARE_LOADING_ANIMATION' => 1,
            'ST_COMPARE_QUICK_ACTIONS' => 1,
            'ST_COMPARE_AUTO_COLLAPSE' => 0,
            'ST_COMPARE_CUSTOM_COLORS' => json_encode([
                'primary' => '#007cba',
                'secondary' => '#666666',
                'accent' => '#ff6b6b',
                'background' => '#ffffff',
                'text' => '#333333'
            ])
        ];

        foreach ($config as $key => $value) {
            if (!Configuration::updateValue($key, $value)) {
                return false;
            }
        }

        return true;
    }

    private function deleteConfiguration()
    {
        $config_keys = [
            'ST_COMPARE_ENABLE',
            'ST_COMPARE_STICKY_FOOTER',
            'ST_COMPARE_THEME',
            'ST_COMPARE_MAX_PRODUCTS',
            'ST_COMPARE_POPUP_MODE',
            'ST_COMPARE_SHOW_PRINT',
            'ST_COMPARE_HIGHLIGHT_DIFFERENCES',
            'ST_COMPARE_HIGHLIGHT_SIMILARITIES',
            'ST_COMPARE_HIDE_DIFFERENCES',
            'ST_COMPARE_HIDE_SIMILARITIES',
            'ST_COMPARE_SHOW_REVIEWS',
            'ST_COMPARE_HOVER_HIGHLIGHT',
            'ST_COMPARE_LOADING_ANIMATION',
            'ST_COMPARE_QUICK_ACTIONS',
            'ST_COMPARE_AUTO_COLLAPSE',
            'ST_COMPARE_CUSTOM_COLORS'
        ];

        foreach ($config_keys as $key) {
            Configuration::deleteByName($key);
        }

        return true;
    }

    public function hookDisplayHeader()
    {
        if (!Configuration::get('ST_COMPARE_ENABLE')) {
            return;
        }

        $this->context->controller->addCSS($this->_path . 'views/css/front.css');
        $this->context->controller->addJS($this->_path . 'views/js/front.js');
        $this->context->controller->addJS($this->_path . 'views/js/sticky-footer-fallback.js');

        // Add custom colors if configured
        $custom_colors = json_decode(Configuration::get('ST_COMPARE_CUSTOM_COLORS'), true);
        if ($custom_colors) {
            $css_vars = ':root {';
            foreach ($custom_colors as $key => $color) {
                $css_vars .= '--st-compare-' . $key . ': ' . $color . ';';
            }
            $css_vars .= '}';
            
            $this->context->controller->addCSS($this->_path . 'views/css/custom-colors.css');
            //Media::addInlineCSS($css_vars);
        }

        return $this->display(__FILE__, 'views/templates/hook/header.tpl');
    }

    public function hookDisplayFooterAfter()
    {
        if (!Configuration::get('ST_COMPARE_ENABLE') || !Configuration::get('ST_COMPARE_STICKY_FOOTER')) {
            return '<!-- CompareX: Module disabled or sticky footer disabled -->';
        }
        $this->context->smarty->assign([
            'st_compare_theme' => Configuration::get('ST_COMPARE_THEME'),
            'st_compare_max_products' => Configuration::get('ST_COMPARE_MAX_PRODUCTS'),
            'st_compare_quick_actions' => Configuration::get('ST_COMPARE_QUICK_ACTIONS'),
            'st_compare_auto_collapse' => Configuration::get('ST_COMPARE_AUTO_COLLAPSE'),
            'compare_link' => $this->context->link->getModuleLink($this->name, 'compare')
        ]);

        return $this->display(__FILE__, 'views/templates/hook/sticky-footer.tpl');
    }

    public function hookDisplayFooter()
    {
        // Alternative hook for older PrestaShop versions
        return $this->hookDisplayFooterAfter();
    }

    public function hookDisplayProductButtons()
    {
        if (!Configuration::get('ST_COMPARE_ENABLE')) {
            return;
        }

        $product = $this->context->controller->getProduct();
        if (!Validate::isLoadedObject($product)) {
            return;
        }

        $this->context->smarty->assign([
            'product' => $product,
            'st_compare_loading' => Configuration::get('ST_COMPARE_LOADING_ANIMATION'),
            'add_to_compare_url' => $this->context->link->getModuleLink($this->name, 'actions', ['action' => 'add', 'id_product' => $product->id])
        ]);

        return $this->display(__FILE__, 'views/templates/hook/product-button.tpl');
    }

    public function hookDisplayProductListFunctionalButtons($params)
    {
        if (!Configuration::get('ST_COMPARE_ENABLE') || !isset($params['product'])) {
            return;
        }

        $product = $params['product'];
        
        $this->context->smarty->assign([
            'product' => $product,
            'st_compare_loading' => Configuration::get('ST_COMPARE_LOADING_ANIMATION'),
            'add_to_compare_url' => $this->context->link->getModuleLink($this->name, 'actions', ['action' => 'add', 'id_product' => $product['id_product']])
        ]);

        return $this->display(__FILE__, 'views/templates/hook/product-list-button.tpl');
    }

    public function hookDisplayQuickView($params)
    {
        if (!Configuration::get('ST_COMPARE_ENABLE') || !isset($params['product'])) {
            return;
        }

        return $this->hookDisplayProductButtons();
    }

    public function hookActionFrontControllerSetMedia()
    {
        if (!Configuration::get('ST_COMPARE_ENABLE')) {
            return;
        }

        // Add global JavaScript variables
        Media::addJsDef([
            'st_compare_ajax_url' => $this->context->link->getModuleLink($this->name, 'actions'),
            'st_compare_max_products' => (int)Configuration::get('ST_COMPARE_MAX_PRODUCTS'),
            'st_compare_popup_mode' => (bool)Configuration::get('ST_COMPARE_POPUP_MODE'),
            'st_compare_loading_animation' => (bool)Configuration::get('ST_COMPARE_LOADING_ANIMATION')
        ]);
    }

    public function getContent()
    {
        $output = '';

        if (Tools::isSubmit('submitStCompareConfig')) {
            $output .= $this->postProcess();
        }

        return $output . $this->displayForm();
    }

    private function postProcess()
    {
        $form_values = $this->getConfigFormValues();
        
        foreach (array_keys($form_values) as $key) {
            Configuration::updateValue($key, Tools::getValue($key));
        }

        // Handle custom colors
        if (Tools::getValue('ST_COMPARE_CUSTOM_COLORS_ENABLE')) {
            $custom_colors = [
                'primary' => Tools::getValue('ST_COMPARE_COLOR_PRIMARY'),
                'secondary' => Tools::getValue('ST_COMPARE_COLOR_SECONDARY'),
                'accent' => Tools::getValue('ST_COMPARE_COLOR_ACCENT'),
                'background' => Tools::getValue('ST_COMPARE_COLOR_BACKGROUND'),
                'text' => Tools::getValue('ST_COMPARE_COLOR_TEXT')
            ];
            Configuration::updateValue('ST_COMPARE_CUSTOM_COLORS', json_encode($custom_colors));
        }

        return $this->displayConfirmation($this->l('Settings updated successfully!'));
    }

    private function displayForm()
    {
        $default_lang = (int)Configuration::get('PS_LANG_DEFAULT');

        $helper = new HelperForm();
        $helper->module = $this;
        $helper->name_controller = $this->name;
        $helper->token = Tools::getAdminTokenLite('AdminModules');
        $helper->currentIndex = AdminController::$currentIndex . '&configure=' . $this->name;

        $helper->default_form_language = $default_lang;
        $helper->allow_employee_form_lang = $default_lang;

        $helper->title = $this->displayName;
        $helper->show_toolbar = true;
        $helper->toolbar_scroll = true;
        $helper->submit_action = 'submitStCompareConfig';
        $helper->toolbar_btn = [
            'save' => [
                'desc' => $this->l('Save'),
                'href' => AdminController::$currentIndex . '&configure=' . $this->name . '&save' . $this->name .
                    '&token=' . Tools::getAdminTokenLite('AdminModules'),
            ],
            'back' => [
                'href' => AdminController::$currentIndex . '&token=' . Tools::getAdminTokenLite('AdminModules'),
                'desc' => $this->l('Back to list')
            ]
        ];

        $helper->fields_value = $this->getConfigFormValues();

        return $helper->generateForm([$this->getConfigForm()]);
    }

    private function getConfigForm()
    {
        return [
            'form' => [
                'legend' => [
                    'title' => $this->l('CompareX Settings'),
                    'icon' => 'icon-cogs',
                ],
                'input' => [
                    [
                        'type' => 'switch',
                        'label' => $this->l('Enable CompareX'),
                        'name' => 'ST_COMPARE_ENABLE',
                        'is_bool' => true,
                        'desc' => $this->l('Enable or disable the product comparison functionality'),
                        'values' => [
                            [
                                'id' => 'active_on',
                                'value' => true,
                                'label' => $this->l('Enabled')
                            ],
                            [
                                'id' => 'active_off',
                                'value' => false,
                                'label' => $this->l('Disabled')
                            ]
                        ],
                    ],
                    [
                        'type' => 'switch',
                        'label' => $this->l('Enable Sticky Footer'),
                        'name' => 'ST_COMPARE_STICKY_FOOTER',
                        'is_bool' => true,
                        'desc' => $this->l('Show a sticky footer with comparison products'),
                        'values' => [
                            [
                                'id' => 'sticky_on',
                                'value' => true,
                                'label' => $this->l('Enabled')
                            ],
                            [
                                'id' => 'sticky_off',
                                'value' => false,
                                'label' => $this->l('Disabled')
                            ]
                        ],
                    ],
                    [
                        'type' => 'select',
                        'label' => $this->l('Theme'),
                        'name' => 'ST_COMPARE_THEME',
                        'desc' => $this->l('Choose a pre-made theme for the comparison interface'),
                        'options' => [
                            'query' => [
                                ['id' => 'theme1', 'name' => $this->l('Default Blue')],
                                ['id' => 'theme2', 'name' => $this->l('Dark Mode')],
                                ['id' => 'theme3', 'name' => $this->l('Minimal')],
                                ['id' => 'theme4', 'name' => $this->l('Colorful')],
                                ['id' => 'custom', 'name' => $this->l('Custom Colors')]
                            ],
                            'id' => 'id',
                            'name' => 'name'
                        ]
                    ],
                    [
                        'type' => 'text',
                        'label' => $this->l('Maximum Products'),
                        'name' => 'ST_COMPARE_MAX_PRODUCTS',
                        'desc' => $this->l('Maximum number of products that can be compared at once'),
                        'class' => 'fixed-width-xs',
                        'suffix' => $this->l('products')
                    ],
                    [
                        'type' => 'switch',
                        'label' => $this->l('Popup Mode'),
                        'name' => 'ST_COMPARE_POPUP_MODE',
                        'is_bool' => true,
                        'desc' => $this->l('Open comparison page in a popup window'),
                        'values' => [
                            [
                                'id' => 'popup_on',
                                'value' => true,
                                'label' => $this->l('Enabled')
                            ],
                            [
                                'id' => 'popup_off',
                                'value' => false,
                                'label' => $this->l('Disabled')
                            ]
                        ],
                    ],
                    [
                        'type' => 'switch',
                        'label' => $this->l('Show Print Button'),
                        'name' => 'ST_COMPARE_SHOW_PRINT',
                        'is_bool' => true,
                        'desc' => $this->l('Show print button on comparison page'),
                        'values' => [
                            [
                                'id' => 'print_on',
                                'value' => true,
                                'label' => $this->l('Enabled')
                            ],
                            [
                                'id' => 'print_off',
                                'value' => false,
                                'label' => $this->l('Disabled')
                            ]
                        ],
                    ],
                    [
                        'type' => 'switch',
                        'label' => $this->l('Highlight Differences'),
                        'name' => 'ST_COMPARE_HIGHLIGHT_DIFFERENCES',
                        'is_bool' => true,
                        'desc' => $this->l('Show button to highlight differences between products'),
                        'values' => [
                            [
                                'id' => 'diff_on',
                                'value' => true,
                                'label' => $this->l('Enabled')
                            ],
                            [
                                'id' => 'diff_off',
                                'value' => false,
                                'label' => $this->l('Disabled')
                            ]
                        ],
                    ],
                    [
                        'type' => 'switch',
                        'label' => $this->l('Show Reviews'),
                        'name' => 'ST_COMPARE_SHOW_REVIEWS',
                        'is_bool' => true,
                        'desc' => $this->l('Display product reviews in comparison table (requires reviews module)'),
                        'values' => [
                            [
                                'id' => 'reviews_on',
                                'value' => true,
                                'label' => $this->l('Enabled')
                            ],
                            [
                                'id' => 'reviews_off',
                                'value' => false,
                                'label' => $this->l('Disabled')
                            ]
                        ],
                    ],
                    [
                        'type' => 'switch',
                        'label' => $this->l('Loading Animation'),
                        'name' => 'ST_COMPARE_LOADING_ANIMATION',
                        'is_bool' => true,
                        'desc' => $this->l('Show loading animation when adding/removing products'),
                        'values' => [
                            [
                                'id' => 'loading_on',
                                'value' => true,
                                'label' => $this->l('Enabled')
                            ],
                            [
                                'id' => 'loading_off',
                                'value' => false,
                                'label' => $this->l('Disabled')
                            ]
                        ],
                    ]
                ],
                'submit' => [
                    'title' => $this->l('Save'),
                ]
            ],
        ];
    }

    private function getConfigFormValues()
    {
        return [
            'ST_COMPARE_ENABLE' => Configuration::get('ST_COMPARE_ENABLE'),
            'ST_COMPARE_STICKY_FOOTER' => Configuration::get('ST_COMPARE_STICKY_FOOTER'),
            'ST_COMPARE_THEME' => Configuration::get('ST_COMPARE_THEME'),
            'ST_COMPARE_MAX_PRODUCTS' => Configuration::get('ST_COMPARE_MAX_PRODUCTS'),
            'ST_COMPARE_POPUP_MODE' => Configuration::get('ST_COMPARE_POPUP_MODE'),
            'ST_COMPARE_SHOW_PRINT' => Configuration::get('ST_COMPARE_SHOW_PRINT'),
            'ST_COMPARE_HIGHLIGHT_DIFFERENCES' => Configuration::get('ST_COMPARE_HIGHLIGHT_DIFFERENCES'),
            'ST_COMPARE_HIGHLIGHT_SIMILARITIES' => Configuration::get('ST_COMPARE_HIGHLIGHT_SIMILARITIES'),
            'ST_COMPARE_HIDE_DIFFERENCES' => Configuration::get('ST_COMPARE_HIDE_DIFFERENCES'),
            'ST_COMPARE_HIDE_SIMILARITIES' => Configuration::get('ST_COMPARE_HIDE_SIMILARITIES'),
            'ST_COMPARE_SHOW_REVIEWS' => Configuration::get('ST_COMPARE_SHOW_REVIEWS'),
            'ST_COMPARE_HOVER_HIGHLIGHT' => Configuration::get('ST_COMPARE_HOVER_HIGHLIGHT'),
            'ST_COMPARE_LOADING_ANIMATION' => Configuration::get('ST_COMPARE_LOADING_ANIMATION'),
            'ST_COMPARE_QUICK_ACTIONS' => Configuration::get('ST_COMPARE_QUICK_ACTIONS'),
            'ST_COMPARE_AUTO_COLLAPSE' => Configuration::get('ST_COMPARE_AUTO_COLLAPSE')
        ];
    }
}
