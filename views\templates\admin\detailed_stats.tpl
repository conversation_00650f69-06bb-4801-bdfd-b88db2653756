{*
* CompareX - Detailed Statistics Template
* 
* <AUTHOR>
* @copyright Copyright (c) 2024 ST-themes
* @license   Academic Free License (AFL 3.0)
*}

<div class="st-compare-detailed-stats">
    <div class="panel-heading">
        <h2>
            <i class="icon-bar-chart"></i>
            {l s='Detailed Comparison Statistics' mod='stproductcompare'}
        </h2>
        <a href="{$back_url|escape:'html':'UTF-8'}" class="btn btn-default pull-right">
            <i class="icon-arrow-left"></i>
            {l s='Back to Management' mod='stproductcompare'}
        </a>
        <div class="clearfix"></div>
    </div>

    {* Overview Stats *}
    <div class="st-compare-stats-section">
        <h4>{l s='Overview Statistics' mod='stproductcompare'}</h4>
        <div class="st-compare-stats-grid">
            <div class="st-compare-stat-card">
                <span class="st-compare-stat-number">{$stats.total_sessions|intval}</span>
                <span class="st-compare-stat-label">{l s='Total Sessions' mod='stproductcompare'}</span>
            </div>
            
            <div class="st-compare-stat-card">
                <span class="st-compare-stat-number">{$stats.active_sessions|intval}</span>
                <span class="st-compare-stat-label">{l s='Active Sessions (24h)' mod='stproductcompare'}</span>
            </div>
            
            <div class="st-compare-stat-card">
                <span class="st-compare-stat-number">{$stats.sessions_with_products|intval}</span>
                <span class="st-compare-stat-label">{l s='Sessions with Products' mod='stproductcompare'}</span>
            </div>
            
            <div class="st-compare-stat-card">
                <span class="st-compare-stat-number">{$stats.avg_products|floatval}</span>
                <span class="st-compare-stat-label">{l s='Average Products per Session' mod='stproductcompare'}</span>
            </div>
        </div>
    </div>

    {* Customer vs Guest Usage *}
    <div class="st-compare-stats-section">
        <h4>{l s='User Type Distribution' mod='stproductcompare'}</h4>
        <div class="st-compare-usage-stats">
            <div class="st-compare-usage-card">
                <div class="st-compare-usage-number st-compare-usage-customers">
                    {$stats.customer_usage.customers|intval}
                </div>
                <div class="st-compare-stat-label">{l s='Registered Customers' mod='stproductcompare'}</div>
            </div>
            
            <div class="st-compare-usage-card">
                <div class="st-compare-usage-number st-compare-usage-guests">
                    {$stats.customer_usage.guests|intval}
                </div>
                <div class="st-compare-stat-label">{l s='Guest Users' mod='stproductcompare'}</div>
            </div>
        </div>
    </div>

    {* Daily Activity *}
    {if $stats.daily_activity && count($stats.daily_activity) > 0}
        <div class="st-compare-stats-section">
            <h4>{l s='Daily Activity (Last 30 Days)' mod='stproductcompare'}</h4>
            <div class="st-compare-chart-container">
                <div class="st-compare-activity-list">
                    {foreach from=$stats.daily_activity item=activity}
                        <div class="st-compare-activity-item">
                            <span>{$activity.date|date_format:'%Y-%m-%d'}</span>
                            <span>
                                <strong>{$activity.sessions|intval}</strong> 
                                {if $activity.sessions == 1}
                                    {l s='session' mod='stproductcompare'}
                                {else}
                                    {l s='sessions' mod='stproductcompare'}
                                {/if}
                            </span>
                        </div>
                    {/foreach}
                </div>
            </div>
        </div>
    {/if}

    {* Top Products *}
    {if $stats.top_products && count($stats.top_products) > 0}
        <div class="st-compare-stats-section">
            <h4>{l s='Most Compared Products' mod='stproductcompare'}</h4>
            <div class="st-compare-chart-container">
                <ul class="st-compare-product-list">
                    {foreach from=$stats.top_products item=product}
                        <li class="st-compare-product-item">
                            <span class="st-compare-product-name">
                                {$product.name|escape:'html':'UTF-8'}
                                <small>(ID: {$product.id_product|intval})</small>
                            </span>
                            <span class="st-compare-product-count">
                                {$product.comparison_count|intval} 
                                {if $product.comparison_count == 1}
                                    {l s='comparison' mod='stproductcompare'}
                                {else}
                                    {l s='comparisons' mod='stproductcompare'}
                                {/if}
                            </span>
                        </li>
                    {/foreach}
                </ul>
            </div>
        </div>
    {/if}

    {* Category Statistics *}
    {if $stats.category_stats && count($stats.category_stats) > 0}
        <div class="st-compare-stats-section">
            <h4>{l s='Most Compared Categories' mod='stproductcompare'}</h4>
            <div class="st-compare-chart-container">
                <ul class="st-compare-product-list">
                    {foreach from=$stats.category_stats item=category}
                        <li class="st-compare-product-item">
                            <span class="st-compare-product-name">
                                {$category.name|escape:'html':'UTF-8'}
                                <small>(ID: {$category.id_category|intval})</small>
                            </span>
                            <span class="st-compare-product-count">
                                {$category.comparison_count|intval} 
                                {if $category.comparison_count == 1}
                                    {l s='comparison' mod='stproductcompare'}
                                {else}
                                    {l s='comparisons' mod='stproductcompare'}
                                {/if}
                            </span>
                        </li>
                    {/foreach}
                </ul>
            </div>
        </div>
    {/if}

    {* Actions *}
    <div class="st-compare-stats-section">
        <h4>{l s='Management Actions' mod='stproductcompare'}</h4>
        <div class="text-center">
            <a href="{$back_url|escape:'html':'UTF-8'}&action=cleanup" 
               class="st-compare-action-btn danger"
               onclick="return confirm('{l s='This will remove comparison sessions older than 30 days. Continue?' mod='stproductcompare'}')">
                <i class="icon-eraser"></i>
                {l s='Cleanup Old Data' mod='stproductcompare'}
            </a>
            
            <a href="{$back_url|escape:'html':'UTF-8'}" class="st-compare-action-btn secondary">
                <i class="icon-list"></i>
                {l s='View All Sessions' mod='stproductcompare'}
            </a>
        </div>
    </div>
</div>

<style>
.panel-heading h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.st-compare-detailed-stats .panel-heading {
    background: #f8f9fa;
    border-bottom: 1px solid #ddd;
    padding: 15px 20px;
    margin: -20px -20px 20px -20px;
    border-radius: 4px 4px 0 0;
}
</style>
