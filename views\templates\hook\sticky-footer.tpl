{*
* CompareX - Sticky Footer Template
* 
* <AUTHOR>
* @copyright Copyright (c) 2024 ST-themes
* @license   Academic Free License (AFL 3.0)
*}

{* Sticky footer is created dynamically by JavaScript *}
{* This template can be used for server-side rendering if needed *}

<script type="text/javascript">
    document.addEventListener('DOMContentLoaded', function() {
        // Additional sticky footer configuration
        if (window.compareX) {
            window.compareX.stickyFooterConfig = {
                theme: '{$st_compare_theme|escape:'javascript':'UTF-8'}',
                maxProducts: {$st_compare_max_products|intval},
                quickActions: {if $st_compare_quick_actions}true{else}false{/if},
                autoCollapse: {if $st_compare_auto_collapse}true{else}false{/if},
                compareLink: '{$compare_link|escape:'javascript':'UTF-8'}'
            };
        }
    });
</script>
