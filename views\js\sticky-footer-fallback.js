/**
 * CompareX - Sticky Footer Fallback
 * This creates the sticky footer using pure JavaScript if the template hook fails
 */

document.addEventListener('DOMContentLoaded', function() {
    // Check if sticky footer already exists
    if (document.getElementById('st-compare-footer')) {
        console.log('CompareX: Sticky footer already exists');
        return;
    }

    console.log('CompareX: Creating fallback sticky footer');

    // Create sticky footer HTML
    const stickyFooterHTML = `
        <div class="st-compare-sticky-footer" id="st-compare-footer" style="display: none;">
            <div class="st-compare-footer-header">
                <div class="st-compare-footer-title">
                    <i class="fa fa-balance-scale"></i>
                    Product Comparison
                    <span class="st-compare-footer-count">0</span>
                </div>
                <button class="st-compare-footer-toggle" title="Toggle comparison tray">
                    <i class="fa fa-chevron-up"></i>
                </button>
            </div>
            <div class="st-compare-footer-content">
                <div class="st-compare-products-grid"></div>
                <div class="st-compare-footer-actions">
                    <a href="#" class="st-compare-action-btn primary st-compare-now">
                        <i class="fa fa-balance-scale"></i>
                        Compare Now
                    </a>
                    <button class="st-compare-action-btn secondary st-compare-clear">
                        <i class="fa fa-trash"></i>
                        Clear All
                    </button>
                </div>
            </div>
        </div>
    `;

    // Add to body
    document.body.insertAdjacentHTML('beforeend', stickyFooterHTML);

    // Add basic CSS if not already present
    if (!document.getElementById('st-compare-fallback-css')) {
        const css = `
            <style id="st-compare-fallback-css">
                .st-compare-sticky-footer {
                    position: fixed;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    background: #ffffff;
                    border-top: 2px solid #007cba;
                    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
                    z-index: 1000;
                    transform: translateY(100%);
                    transition: transform 0.3s ease;
                }
                
                .st-compare-sticky-footer.visible {
                    transform: translateY(0);
                    display: block !important;
                }
                
                .st-compare-footer-header {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 12px 20px;
                    background: #007cba;
                    color: white;
                    cursor: pointer;
                }
                
                .st-compare-footer-title {
                    font-weight: 600;
                    font-size: 16px;
                }
                
                .st-compare-footer-count {
                    background: #ff6b6b;
                    color: white;
                    padding: 4px 8px;
                    border-radius: 12px;
                    font-size: 12px;
                    font-weight: 600;
                    margin-left: 10px;
                }
                
                .st-compare-footer-toggle {
                    background: none;
                    border: none;
                    color: white;
                    font-size: 18px;
                    cursor: pointer;
                    padding: 4px;
                }
                
                .st-compare-footer-content {
                    padding: 20px;
                    max-height: 300px;
                    overflow-y: auto;
                }
                
                .st-compare-products-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 16px;
                    margin-bottom: 20px;
                }
                
                .st-compare-product-card {
                    background: white;
                    border: 1px solid #ddd;
                    border-radius: 8px;
                    padding: 16px;
                    text-align: center;
                    position: relative;
                }
                
                .st-compare-product-image {
                    width: 80px;
                    height: 80px;
                    object-fit: cover;
                    border-radius: 4px;
                    margin-bottom: 12px;
                }
                
                .st-compare-product-name {
                    font-size: 14px;
                    font-weight: 500;
                    margin-bottom: 8px;
                }
                
                .st-compare-product-price {
                    font-size: 16px;
                    font-weight: 600;
                    color: #007cba;
                    margin-bottom: 12px;
                }
                
                .st-compare-product-remove {
                    position: absolute;
                    top: 8px;
                    right: 8px;
                    width: 24px;
                    height: 24px;
                    background: #ff6b6b;
                    color: white;
                    border: none;
                    border-radius: 50%;
                    cursor: pointer;
                    font-size: 12px;
                }
                
                .st-compare-footer-actions {
                    display: flex;
                    gap: 12px;
                    justify-content: center;
                    padding-top: 16px;
                    border-top: 1px solid #eee;
                }
                
                .st-compare-action-btn {
                    padding: 12px 24px;
                    border: none;
                    border-radius: 6px;
                    font-size: 14px;
                    font-weight: 500;
                    cursor: pointer;
                    text-decoration: none;
                    display: inline-flex;
                    align-items: center;
                    gap: 8px;
                }
                
                .st-compare-action-btn.primary {
                    background: #007cba;
                    color: white;
                }
                
                .st-compare-action-btn.secondary {
                    background: #6c757d;
                    color: white;
                }
            </style>
        `;
        document.head.insertAdjacentHTML('beforeend', css);
    }

    console.log('CompareX: Fallback sticky footer created');
});
