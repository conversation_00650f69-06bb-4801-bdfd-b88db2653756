{*
* CompareX - Admin Stats Panel Template
* 
* <AUTHOR>
* @copyright Copyright (c) 2024 ST-themes
* @license   Academic Free License (AFL 3.0)
*}

<div class="st-compare-stats-panel">
    <h3>
        <i class="icon-bar-chart"></i>
        {l s='Comparison Statistics Overview' mod='stproductcompare'}
    </h3>
    
    <div class="st-compare-stats-grid">
        <div class="st-compare-stat-card">
            <span class="st-compare-stat-number">{$stats.total_sessions|intval}</span>
            <span class="st-compare-stat-label">{l s='Total Sessions' mod='stproductcompare'}</span>
        </div>
        
        <div class="st-compare-stat-card">
            <span class="st-compare-stat-number">{$stats.active_sessions|intval}</span>
            <span class="st-compare-stat-label">{l s='Active (24h)' mod='stproductcompare'}</span>
        </div>
        
        <div class="st-compare-stat-card">
            <span class="st-compare-stat-number">{$stats.sessions_with_products|intval}</span>
            <span class="st-compare-stat-label">{l s='With Products' mod='stproductcompare'}</span>
        </div>
        
        <div class="st-compare-stat-card">
            <span class="st-compare-stat-number">{$stats.avg_products|floatval}</span>
            <span class="st-compare-stat-label">{l s='Avg Products/Session' mod='stproductcompare'}</span>
        </div>
    </div>
    
    {if $stats.top_products && count($stats.top_products) > 0}
        <div class="st-compare-top-products">
            <h4>
                <i class="icon-trophy"></i>
                {l s='Most Compared Products' mod='stproductcompare'}
            </h4>
            <ul class="st-compare-product-list">
                {foreach from=$stats.top_products item=product}
                    <li class="st-compare-product-item">
                        <span class="st-compare-product-name">{$product.name|escape:'html':'UTF-8'}</span>
                        <span class="st-compare-product-count">{$product.comparison_count|intval}</span>
                    </li>
                {/foreach}
            </ul>
        </div>
    {/if}
</div>
