# CompareX Testing Guide

## 🚀 Quick Test

### Step 1: Install Module
1. Go to **Admin → Mo<PERSON>les → Module Manager**
2. Find **"CompareX - Advanced Product Comparison"**
3. Click **Install**

### Step 2: Configure Module
1. Click **Configure** on the module
2. Set these options:
   - ✅ **Enable CompareX**: YES
   - ✅ **Enable Sticky Footer**: YES
   - **Theme**: Default Blue
   - **Maximum Products**: 4
3. Click **Save**

### Step 3: Test the Sticky Footer

#### Method 1: Debug Mode
Add `?debug=1` to any page URL:
```
yoursite.com/?debug=1
yoursite.com/category-page?debug=1
yoursite.com/product-page?debug=1
```

You should see:
- 🚀 **Debug panel** in top-right corner
- **Test buttons** to add products
- **Status indicators** showing if CompareX is working

#### Method 2: Auto Test Mode
Add `?compare_test=1` to any page URL:
```
yoursite.com/?compare_test=1
```

This will:
- Show debug panel
- Automatically add a test product after 2 seconds
- Display the sticky footer

#### Method 3: Manual Test
1. Go to any **product page**
2. Look for **"Add to Compare"** button
3. Click it
4. **Sticky footer should appear** at bottom of page

### Step 4: Check Installation
Visit: `yoursite.com/modules/stproductcompare/check-installation.php`

This shows:
- ✅ Module installation status
- ✅ Configuration settings
- ✅ Database tables
- ✅ File existence
- 🧪 **Live test buttons**

## 🔧 Troubleshooting

### Issue: Sticky Footer Not Showing

#### Solution 1: Check Browser Console
1. Press **F12** → **Console** tab
2. Look for these messages:
   - ✅ `"CompareX: Initializing..."`
   - ✅ `"CompareX: Creating sticky footer..."`
   - ✅ `"CompareX: Sticky footer created successfully"`

#### Solution 2: Force Display
Add this CSS temporarily to test:
```css
#st-compare-footer {
    display: block !important;
    position: fixed !important;
    bottom: 0 !important;
    background: red !important;
    z-index: 99999 !important;
    height: 100px !important;
}
```

#### Solution 3: Check jQuery
Make sure jQuery is loaded. In browser console, type:
```javascript
typeof $
```
Should return `"function"`, not `"undefined"`

#### Solution 4: Manual Footer Creation
Add this to your theme's footer:
```html
<script>
document.addEventListener('DOMContentLoaded', function() {
    if (!document.getElementById('st-compare-footer')) {
        document.body.innerHTML += '<div id="st-compare-footer" style="position:fixed;bottom:0;left:0;right:0;background:#007cba;color:white;padding:20px;z-index:9999;">COMPAREX TEST FOOTER - IT WORKS!</div>';
    }
});
</script>
```

### Issue: JavaScript Errors

#### Check for Common Issues:
1. **jQuery not loaded**: Add jQuery to your theme
2. **FontAwesome missing**: Icons won't show but functionality works
3. **CSS conflicts**: Footer might be hidden by theme CSS

#### Debug JavaScript:
```javascript
// Check if CompareX is loaded
console.log(typeof window.compareX);

// Check configuration
console.log(window.st_compare_config);

// Test functions
testAddToCompare(1);
showCompareFooter();
```

## 📱 Testing Checklist

### ✅ Basic Functionality
- [ ] Module installs without errors
- [ ] Configuration saves correctly
- [ ] Debug panel appears with `?debug=1`
- [ ] Test buttons work in debug panel
- [ ] Sticky footer appears when products added

### ✅ Sticky Footer Features
- [ ] Footer slides up from bottom
- [ ] Product count updates correctly
- [ ] Products display with images and prices
- [ ] "Compare Now" button works
- [ ] "Clear All" button works
- [ ] Footer can be collapsed/expanded

### ✅ Product Integration
- [ ] "Add to Compare" buttons appear on product pages
- [ ] "Add to Compare" buttons appear in product lists
- [ ] Buttons show loading animation
- [ ] Button text changes to "In Comparison"
- [ ] Maximum product limit is enforced

### ✅ Comparison Page
- [ ] Compare page loads without errors
- [ ] Products display in comparison table
- [ ] Features are compared correctly
- [ ] Print button works (if enabled)
- [ ] Filter buttons work (if enabled)

## 🎯 Expected Behavior

### When Working Correctly:
1. **Page Load**: Debug panel appears (if `?debug=1`)
2. **Add Product**: Sticky footer slides up from bottom
3. **Product Display**: Shows image, name, price, remove button
4. **Multiple Products**: Grid layout with up to 4 products
5. **Compare Now**: Opens comparison page or popup
6. **Clear All**: Hides footer and resets count

### Visual Indicators:
- 🟢 **Green status** in debug panel = Working
- 🔴 **Red status** in debug panel = Problem
- 📊 **Count updates** = AJAX working
- 👁️ **Footer visible** = CSS working

## 🚨 Emergency Fix

If nothing works, add this to your theme's `footer.tpl`:

```html
<!-- CompareX Emergency Footer -->
<div id="emergency-compare-footer" style="position:fixed;bottom:0;left:0;right:0;background:#007cba;color:white;padding:15px;z-index:9999;display:none;">
    <div style="display:flex;justify-content:space-between;align-items:center;">
        <div>
            <strong>Product Comparison (<span id="emergency-count">0</span>)</strong>
        </div>
        <div>
            <button onclick="document.getElementById('emergency-compare-footer').style.display='none'" style="background:none;border:none;color:white;cursor:pointer;">✕</button>
        </div>
    </div>
</div>

<script>
function emergencyAddToCompare() {
    const footer = document.getElementById('emergency-compare-footer');
    const count = document.getElementById('emergency-count');
    const currentCount = parseInt(count.textContent) + 1;
    count.textContent = currentCount;
    footer.style.display = 'block';
}

// Test button
document.body.innerHTML += '<button onclick="emergencyAddToCompare()" style="position:fixed;top:10px;right:10px;z-index:10000;background:red;color:white;padding:10px;border:none;cursor:pointer;">EMERGENCY TEST</button>';
</script>
```

This creates a basic working footer for testing purposes.

---

**Need Help?** Check the browser console for error messages and compare with the expected behavior above.
