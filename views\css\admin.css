/**
 * CompareX - Admin Styles
 * 
 * <AUTHOR>
 * @copyright Copyright (c) 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

/* Stats Panel */
.st-compare-stats-panel {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 20px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.st-compare-stats-panel h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.st-compare-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.st-compare-stat-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 16px;
    text-align: center;
    transition: all 0.3s ease;
}

.st-compare-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.st-compare-stat-number {
    font-size: 28px;
    font-weight: 700;
    color: #007cba;
    margin-bottom: 8px;
    display: block;
}

.st-compare-stat-label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.st-compare-top-products {
    margin-top: 20px;
}

.st-compare-top-products h4 {
    margin-bottom: 15px;
    color: #333;
    font-size: 16px;
}

.st-compare-product-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.st-compare-product-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.st-compare-product-item:last-child {
    border-bottom: none;
}

.st-compare-product-name {
    font-weight: 500;
    color: #333;
}

.st-compare-product-count {
    background: #007cba;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

/* Session View */
.st-compare-session-view {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
}

.st-compare-session-header {
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
    margin-bottom: 20px;
}

.st-compare-session-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.st-compare-info-group {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
}

.st-compare-info-label {
    font-weight: 600;
    color: #666;
    font-size: 12px;
    text-transform: uppercase;
    margin-bottom: 5px;
}

.st-compare-info-value {
    font-size: 14px;
    color: #333;
}

.st-compare-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
}

.st-compare-product-card {
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 15px;
    text-align: center;
    background: #fff;
    transition: all 0.3s ease;
}

.st-compare-product-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.st-compare-product-image {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 4px;
    margin-bottom: 10px;
}

.st-compare-product-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    font-size: 14px;
    line-height: 1.3;
}

.st-compare-product-ref {
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
}

.st-compare-product-price {
    font-size: 16px;
    font-weight: 700;
    color: #007cba;
    margin-bottom: 10px;
}

.st-compare-product-link {
    display: inline-block;
    padding: 6px 12px;
    background: #007cba;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    font-size: 12px;
    transition: background 0.3s ease;
}

.st-compare-product-link:hover {
    background: #005a8b;
    color: white;
    text-decoration: none;
}

/* Detailed Stats */
.st-compare-detailed-stats {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
}

.st-compare-stats-section {
    margin-bottom: 30px;
}

.st-compare-stats-section h4 {
    margin-bottom: 15px;
    color: #333;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 2px solid #007cba;
    padding-bottom: 8px;
}

.st-compare-chart-container {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

.st-compare-activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.st-compare-activity-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.st-compare-activity-item:last-child {
    border-bottom: none;
}

.st-compare-usage-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.st-compare-usage-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
}

.st-compare-usage-number {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 8px;
}

.st-compare-usage-customers {
    color: #28a745;
}

.st-compare-usage-guests {
    color: #ffc107;
}

/* Responsive Design */
@media (max-width: 768px) {
    .st-compare-stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
    }
    
    .st-compare-session-info {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .st-compare-products-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 10px;
    }
    
    .st-compare-usage-stats {
        grid-template-columns: 1fr;
        gap: 15px;
    }
}

/* Table customizations */
.table .st-compare-customer-info {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.table .st-compare-product-names {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Action buttons */
.st-compare-action-btn {
    display: inline-block;
    padding: 8px 16px;
    margin: 0 5px;
    background: #007cba;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.st-compare-action-btn:hover {
    background: #005a8b;
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
}

.st-compare-action-btn.secondary {
    background: #6c757d;
}

.st-compare-action-btn.secondary:hover {
    background: #545b62;
}

.st-compare-action-btn.danger {
    background: #dc3545;
}

.st-compare-action-btn.danger:hover {
    background: #c82333;
}

/* Loading states */
.st-compare-loading {
    text-align: center;
    padding: 40px;
    color: #666;
}

.st-compare-loading i {
    font-size: 24px;
    margin-bottom: 10px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
