<?php
/**
 * CompareX - Test Controller
 * 
 * <AUTHOR>
 * @copyright Copyright (c) 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

class StProductCompareTestModuleFrontController extends ModuleFrontController
{
    public function initContent()
    {
        parent::initContent();

        // Test if module is working
        $this->context->smarty->assign([
            'module_enabled' => Configuration::get('ST_COMPARE_ENABLE'),
            'sticky_footer_enabled' => Configuration::get('ST_COMPARE_STICKY_FOOTER'),
            'theme' => Configuration::get('ST_COMPARE_THEME'),
            'max_products' => Configuration::get('ST_COMPARE_MAX_PRODUCTS'),
            'test_products' => $this->getTestProducts()
        ]);

        $this->setTemplate('module:stproductcompare/views/templates/front/test.tpl');
    }

    private function getTestProducts()
    {
        // Get some sample products for testing
        $products = Product::getProducts($this->context->language->id, 0, 5, 'id_product', 'ASC');
        
        $test_products = [];
        foreach ($products as $product) {
            $test_products[] = [
                'id_product' => $product['id_product'],
                'name' => $product['name'],
                'price' => Tools::displayPrice($product['price']),
                'link' => $this->context->link->getProductLink($product['id_product'])
            ];
        }
        
        return $test_products;
    }
}
