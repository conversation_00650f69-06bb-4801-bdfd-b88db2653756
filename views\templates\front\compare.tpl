{*
* CompareX - Comparison Page Template
* 
* <AUTHOR>
* @copyright Copyright (c) 2024 ST-themes
* @license   Academic Free License (AFL 3.0)
*}

{extends file='page.tpl'}

{block name='page_title'}
    {l s='Product Comparison' mod='stproductcompare'}
{/block}

{block name='page_content'}
    <div class="st-compare-page">
        {if $empty_comparison}
            <div class="st-compare-empty">
                <div class="alert alert-info text-center">
                    <i class="fa fa-balance-scale fa-3x mb-3"></i>
                    <h3>{l s='No products to compare' mod='stproductcompare'}</h3>
                    <p>{$message}</p>
                    <a href="{$urls.pages.index}" class="btn btn-primary">
                        <i class="fa fa-home"></i>
                        {l s='Continue Shopping' mod='stproductcompare'}
                    </a>
                </div>
            </div>
        {else}
            <div class="st-compare-header">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h1 class="h3 mb-0">
                            <i class="fa fa-balance-scale"></i>
                            {l s='Comparing %d Products' sprintf=[$products|count] mod='stproductcompare'}
                        </h1>
                    </div>
                    <div class="col-md-6 text-md-right">
                        <div class="st-compare-actions">
                            {if $show_print}
                                <button class="btn btn-outline-secondary st-compare-print">
                                    <i class="fa fa-print"></i>
                                    {l s='Print Comparison' mod='stproductcompare'}
                                </button>
                            {/if}
                            <button class="btn btn-outline-danger st-compare-clear">
                                <i class="fa fa-trash"></i>
                                {l s='Clear All' mod='stproductcompare'}
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            {if $highlight_differences || $highlight_similarities || $hide_differences || $hide_similarities}
                <div class="st-compare-filters mb-4">
                    <div class="btn-group" role="group">
                        {if $highlight_differences}
                            <button class="btn btn-outline-primary" id="highlight-differences">
                                <i class="fa fa-eye"></i>
                                {l s='Highlight Differences' mod='stproductcompare'}
                            </button>
                        {/if}
                        {if $highlight_similarities}
                            <button class="btn btn-outline-success" id="highlight-similarities">
                                <i class="fa fa-check"></i>
                                {l s='Highlight Similarities' mod='stproductcompare'}
                            </button>
                        {/if}
                        {if $hide_differences}
                            <button class="btn btn-outline-warning" id="hide-differences">
                                <i class="fa fa-eye-slash"></i>
                                {l s='Hide Differences' mod='stproductcompare'}
                            </button>
                        {/if}
                        {if $hide_similarities}
                            <button class="btn btn-outline-info" id="hide-similarities">
                                <i class="fa fa-minus"></i>
                                {l s='Hide Similarities' mod='stproductcompare'}
                            </button>
                        {/if}
                    </div>
                </div>
            {/if}

            <div class="st-compare-table-container">
                <table class="st-compare-table table table-bordered{if $hover_highlight} table-hover{/if}">
                    <thead>
                        <tr>
                            <th class="feature-name">{l s='Feature' mod='stproductcompare'}</th>
                            {foreach from=$products item=product}
                                <th class="product-header">
                                    <div class="product-info">
                                        <img src="{$product.image}" alt="{$product.name}" class="product-image">
                                        <div class="product-name">{$product.name}</div>
                                        <div class="product-price">{$product.price_formatted}</div>
                                        <div class="product-actions mt-2">
                                            <a href="{$product.link}" class="btn btn-primary btn-sm">
                                                <i class="fa fa-eye"></i>
                                                {l s='View Product' mod='stproductcompare'}
                                            </a>
                                            <button class="btn btn-outline-danger btn-sm st-compare-remove" data-product-id="{$product.id_product}">
                                                <i class="fa fa-times"></i>
                                                {l s='Remove' mod='stproductcompare'}
                                            </button>
                                        </div>
                                    </div>
                                </th>
                            {/foreach}
                        </tr>
                    </thead>
                    <tbody>
                        {* Basic Information *}
                        <tr class="compare-row" data-feature="price">
                            <td class="feature-name font-weight-bold">{l s='Price' mod='stproductcompare'}</td>
                            {foreach from=$products item=product}
                                <td class="feature-value">{$product.price_formatted}</td>
                            {/foreach}
                        </tr>

                        {if $products[0].reference}
                            <tr class="compare-row" data-feature="reference">
                                <td class="feature-name font-weight-bold">{l s='Reference' mod='stproductcompare'}</td>
                                {foreach from=$products item=product}
                                    <td class="feature-value">{$product.reference|default:'-'}</td>
                                {/foreach}
                            </tr>
                        {/if}

                        <tr class="compare-row" data-feature="description">
                            <td class="feature-name font-weight-bold">{l s='Description' mod='stproductcompare'}</td>
                            {foreach from=$products item=product}
                                <td class="feature-value">{$product.description_short|strip_tags|truncate:100}</td>
                            {/foreach}
                        </tr>

                        {* Product Features *}
                        {foreach from=$features key=feature_id item=feature_name}
                            <tr class="compare-row" data-feature="feature-{$feature_id}">
                                <td class="feature-name font-weight-bold">{$feature_name}</td>
                                {foreach from=$products item=product}
                                    <td class="feature-value">
                                        {if isset($product.features[$feature_id])}
                                            {$product.features[$feature_id].value}
                                        {else}
                                            <span class="text-muted">-</span>
                                        {/if}
                                    </td>
                                {/foreach}
                            </tr>
                        {/foreach}

                        {* Product Attributes *}
                        {foreach from=$attributes key=attribute_id item=attribute_name}
                            <tr class="compare-row" data-feature="attribute-{$attribute_id}">
                                <td class="feature-name font-weight-bold">{$attribute_name}</td>
                                {foreach from=$products item=product}
                                    <td class="feature-value">
                                        {if isset($product.attributes[$attribute_id])}
                                            {', '|implode:$product.attributes[$attribute_id].values}
                                        {else}
                                            <span class="text-muted">-</span>
                                        {/if}
                                    </td>
                                {/foreach}
                            </tr>
                        {/foreach}

                        {* Reviews if enabled *}
                        {if $show_reviews}
                            <tr class="compare-row" data-feature="reviews">
                                <td class="feature-name font-weight-bold">{l s='Reviews' mod='stproductcompare'}</td>
                                {foreach from=$products item=product}
                                    <td class="feature-value">
                                        {if $product.reviews}
                                            {$product.reviews nofilter}
                                        {else}
                                            <span class="text-muted">{l s='No reviews' mod='stproductcompare'}</span>
                                        {/if}
                                    </td>
                                {/foreach}
                            </tr>
                        {/if}
                    </tbody>
                </table>
            </div>

            <div class="st-compare-footer-actions text-center mt-4">
                <a href="{$urls.pages.index}" class="btn btn-outline-secondary">
                    <i class="fa fa-arrow-left"></i>
                    {l s='Continue Shopping' mod='stproductcompare'}
                </a>
                <button class="btn btn-danger st-compare-clear">
                    <i class="fa fa-trash"></i>
                    {l s='Clear Comparison' mod='stproductcompare'}
                </button>
            </div>
        {/if}
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Comparison table functionality
            const compareTable = {
                init: function() {
                    this.bindEvents();
                    this.detectDifferences();
                },

                bindEvents: function() {
                    // Filter buttons
                    $('#highlight-differences').on('click', () => this.highlightDifferences());
                    $('#highlight-similarities').on('click', () => this.highlightSimilarities());
                    $('#hide-differences').on('click', () => this.hideDifferences());
                    $('#hide-similarities').on('click', () => this.hideSimilarities());
                },

                detectDifferences: function() {
                    $('.compare-row').each(function() {
                        const $row = $(this);
                        const values = [];
                        
                        $row.find('.feature-value').each(function() {
                            values.push($(this).text().trim());
                        });

                        const unique = [...new Set(values)];
                        const isDifferent = unique.length > 1;
                        
                        $row.attr('data-different', isDifferent);
                    });
                },

                highlightDifferences: function() {
                    $('.compare-row').removeClass('table-warning table-success');
                    $('.compare-row[data-different="true"]').addClass('table-warning');
                },

                highlightSimilarities: function() {
                    $('.compare-row').removeClass('table-warning table-success');
                    $('.compare-row[data-different="false"]').addClass('table-success');
                },

                hideDifferences: function() {
                    $('.compare-row').show();
                    $('.compare-row[data-different="true"]').hide();
                },

                hideSimilarities: function() {
                    $('.compare-row').show();
                    $('.compare-row[data-different="false"]').hide();
                }
            };

            compareTable.init();
        });
    </script>
{/block}

<style>
.st-compare-page {
    padding: 20px 0;
}

.st-compare-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.st-compare-actions .btn {
    margin-left: 10px;
}

.st-compare-filters {
    text-align: center;
}

.st-compare-table {
    font-size: 14px;
}

.st-compare-table .feature-name {
    background: #f8f9fa;
    font-weight: 600;
    min-width: 200px;
    vertical-align: middle;
}

.st-compare-table .product-header {
    text-align: center;
    vertical-align: top;
    min-width: 200px;
}

.st-compare-table .product-image {
    width: 120px;
    height: 120px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 12px;
}

.st-compare-table .product-name {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    line-height: 1.3;
}

.st-compare-table .product-price {
    font-size: 18px;
    font-weight: 700;
    color: var(--st-compare-primary, #007cba);
    margin-bottom: 12px;
}

.st-compare-table .feature-value {
    vertical-align: middle;
    padding: 12px 16px;
}

.st-compare-empty {
    text-align: center;
    padding: 60px 20px;
}

@media print {
    .st-compare-header .st-compare-actions,
    .st-compare-filters,
    .st-compare-footer-actions {
        display: none !important;
    }
    
    .st-compare-table .product-actions {
        display: none !important;
    }
}

@media (max-width: 768px) {
    .st-compare-table-container {
        overflow-x: auto;
    }
    
    .st-compare-table {
        min-width: 600px;
    }
    
    .st-compare-header .row {
        text-align: center;
    }
    
    .st-compare-actions {
        margin-top: 15px;
    }
    
    .st-compare-actions .btn {
        margin: 5px;
    }
}
</style>
{/block}
